./server.js:28:    console.log(`🚀 Next.js is running with SSL on https://seoanalyser.com.au`);
./server.js:36:    console.log(`🔄 Redirecting HTTP to HTTPS`);
./src/tests/deviceId.test.js:14:  console.log('Testing device ID functionality...');
./src/tests/deviceId.test.js:18:  console.log('Current device ID:', deviceId);
./src/tests/deviceId.test.js:22:  console.log('Device ID from localStorage:', storedDeviceId);
./src/tests/deviceId.test.js:26:    console.log('✅ Test passed: Device IDs match');
./src/tests/deviceId.test.js:28:    console.error('❌ Test failed: Device IDs do not match');
./src/tests/deviceId.test.js:34:    console.log('✅ Test passed: Device ID is consistent');
./src/tests/deviceId.test.js:36:    console.error('❌ Test failed: Device ID changed on second call');
./src/tests/useScrollLock.test.js:110:console.log(`
./src/components/payment/PaymentReceipt.tsx:90:        console.error(`Failed to copy ${type}:`, err);
./src/components/modals/SubscriptionActivateModal.tsx:31:      console.error("Error confirming activation:", error);
./src/components/modals/SEOAuditRequestModal.tsx:163:      console.error("reCAPTCHA verification failed:", error);
./src/components/modals/SEOAuditRequestModal.tsx:193:      console.error("Failed to submit request:", error);
./src/components/modals/SubscriptionCancelModal.tsx:31:      console.error("Error confirming cancellation:", error);
./src/components/footer/Footer.tsx:48:        console.error("Error fetching blog posts:", err);
./src/components/cookie-consent/CookieConsentModal.tsx:48:      console.error("Error saving cookie consent:", error);
./src/components/cookie-consent/CookieConsentModal.tsx:69:      console.error("Error saving cookie preferences:", error);
./src/components/cookie-consent/CookieConsentModal.tsx:874:    console.error("Error resetting consent:", error);
./src/components/cookie-consent/CookiePreferences.tsx:58:      console.error("Error updating cookie preferences:", error);
./src/components/loading/Loading.tsx:237:        console.log("All images loaded");
./src/components/loading/Loading.tsx:240:    const handleImageError = () => console.warn("Image failed to load");
./src/components/auth/AuthModals.tsx:79:      console.log("Auth modal opened with action type:", actionType);
./src/components/auth/AuthModals.tsx:83:        console.log(
./src/components/auth/AuthModals.tsx:98:            console.log(
./src/components/auth/AuthModals.tsx:111:          console.error("Error checking authentication status:", error);
./src/components/auth/AuthModals.tsx:181:    console.log(
./src/components/auth/AuthModals.tsx:191:      console.log("Registration flow success, not calling onSuccess callback");
./src/components/auth/AuthModals.tsx:204:            console.log(
./src/components/auth/AuthModals.tsx:222:        console.error(
./src/components/auth/AuthModals.tsx:239:      console.log("Calling onSuccess callback");
./src/components/auth/LoginForm.tsx:97:      console.log("Login successful, calling success callback with delay");
./src/components/auth/LoginForm.tsx:101:        console.log("Executing login success callback");
./src/components/auth/UserProfileDropdown.tsx:61:          console.log("Auth check failed in dropdown, logging out user");
./src/components/auth/UserProfileDropdown.tsx:66:        console.error("Failed to refresh profile data:", error);
./src/components/auth/UserProfileDropdown.tsx:123:      console.error("Error cancelling subscription:", error);
./src/components/auth/UserProfileDropdown.tsx:155:      console.error("Error reactivating subscription:", error);
./src/components/auth/OtpVerificationForm.tsx:114:      console.log(
./src/components/auth/OtpVerificationForm.tsx:120:        console.log("Executing OTP verification success callback");
./src/components/auth/RegisterForm.tsx:223:      console.log("Registration successful, proceeding to OTP verification");
./src/components/auth/AuthModal.tsx:56:    console.log(
./src/utils/authStateMachine.ts:93:    console.error(`Invalid state: ${currentState}`);
./src/utils/authStateMachine.ts:100:    console.warn(`No transition defined for event ${event.type} in state ${currentState}`);
./src/utils/authErrorHandler.ts:36:  console.log('Auth error detected, notifying listeners');
./src/utils/authErrorHandler.ts:41:      console.error('Error in auth error listener:', error);
./src/store/authStore.ts:124:          console.error("Auth error:", error);
./src/store/authStore.ts:164:              console.log("Login successful, fetching user profile...");
./src/store/authStore.ts:170:                console.log("Profile fetched successfully after login");
./src/store/authStore.ts:176:                console.error("Failed to fetch profile after successful login");
./src/store/authStore.ts:241:              console.log(
./src/store/authStore.ts:249:                console.log(
./src/store/authStore.ts:257:                console.error(
./src/store/authStore.ts:297:            console.log(
./src/store/authStore.ts:312:            console.log(
./src/store/authStore.ts:329:              console.log("Performing auth check");
./src/store/authStore.ts:333:                console.log("Auth check failed: Token is invalid or expired");
./src/store/authStore.ts:344:              console.log("Auth token is valid, fetching user profile data...");
./src/store/authStore.ts:348:                console.log("Profile data fetched successfully:", {
./src/store/authStore.ts:364:                console.error(
./src/store/authStore.ts:378:              console.error("Error during auth check:", error);
./src/store/authStore.ts:478:            console.log("Checking auth status for modal", {
./src/store/authStore.ts:485:              console.log(
./src/store/authStore.ts:498:              console.log(
./src/store/authStore.ts:504:                console.log(
./src/store/authStore.ts:517:            console.log(
./src/store/authStore.ts:524:              console.log("User authenticated after profile check");
./src/store/authStore.ts:534:            console.log(
./src/store/authStore.ts:543:            console.error("Error checking authentication status:", error);
./src/store/authStore.ts:557:          console.log("Auth success handler called", {
./src/store/authStore.ts:571:            console.log("Executing auth modal callback");
./src/store/authStore.ts:618:    console.log("Auth error handler called, opening auth modal");
./src/store/authStore.ts:642:      console.log("Auth error handler initialized successfully");
./src/store/authStore.ts:645:    console.error("Failed to initialize auth error handler:", error);
./src/app/(root)/Providers.tsx:22:        console.log(
./src/app/(root)/Providers.tsx:36:        console.log("Page became visible, checking auth status");
./src/app/(root)/Providers.tsx:62:        console.log("Auth error detected in Providers, opening auth modal");
./src/app/(root)/Providers.tsx:66:        console.log(
./src/app/(root)/blog/page/[page]/page.tsx:33:    console.error("Error generating static params:", error);
./src/app/(root)/blog/page/[page]/page.tsx:173:    console.error(`Error fetching blog page ${pageNum}:`, error);
./src/app/(root)/blog/components/ShareButton.tsx:22:      console.error("Error sharing:", error);
./src/app/(root)/blog/BlogPageClient.tsx:189:            console.error("Error in search API call:", searchErr);
./src/app/(root)/blog/BlogPageClient.tsx:254:        console.error("Error fetching blog posts:", err);
./src/app/(root)/blog/StaticBlogPageClient.tsx:131:        console.error("Error in search API call:", searchErr);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:168:        console.error("Error verifying payment:", error);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:234:        console.error("Error checking Pro Plan subscription:", error);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:258:          console.log("Using mock data for testing...");
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:276:          console.error("Error loading mock data:", error);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:332:          console.log(
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:341:            console.error("Both share and status APIs failed:", statusError);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:351:        console.error("Error fetching analysis data:", err);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:478:              console.warn(`Image timeout (attempt ${attempt}): ${url}`);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:492:              console.log(
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:500:              console.warn(
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:512:                console.warn(`All attempts failed for: ${url}`);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:527:      console.log(
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:533:      console.error("Error preloading images:", error);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:545:        console.log("Starting image preloading...");
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:555:            console.log("PDF ready after image preloading");
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:593:      console.error("Print failed:", error);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:598:      console.log("Print starting, handling CORS images...");
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:610:            console.log("All images processed for print");
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:650:              console.warn(`Fixing image for print: ${img.src}`);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:664:                console.warn(`Hiding failed image: ${img.src}`);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:684:          console.log("Image processing timeout, proceeding with print");
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:781:    console.log("Print requested, handling CORS images...");
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:788:      console.log("Attempting image preload...");
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:790:      console.log(`Image preload result: ${imagesLoaded}`);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:801:      console.log(
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:806:      console.error("Error during print preparation:", error);
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:809:      console.log("Proceeding with print despite image loading issues...");
./src/app/(root)/white-label-pdf/WhiteLabelPdfClient.tsx:831:      console.error("Invalid URL:", url, error);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/BillingAndPaymentStep.tsx:91:      console.log("Using API pricing data:", apiPricingData);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/BillingAndPaymentStep.tsx:101:        console.log("Found plan:", plan);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/BillingAndPaymentStep.tsx:121:    console.log("Using fallback pricing data");
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/BillingAndPaymentStep.tsx:205:      console.log(
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/BillingAndPaymentStep.tsx:221:      console.log("Payment session response:", response);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/BillingAndPaymentStep.tsx:238:      console.error("Payment processing error:", error);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/DownloadStep.tsx:289:    console.log("Retry button clicked"); // Debug log
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/DownloadStep.tsx:291:      console.log("Calling onRetry callback"); // Debug log
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/DownloadStep.tsx:295:      console.log("No onRetry callback, using fallback"); // Debug log
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:127:        console.error("Error reading file for preview");
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:307:            console.warn("Image validation timed out");
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:316:            console.log(
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:333:            console.error("Image failed to load in validation");
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:345:          console.error("FileReader error:", reader.error);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:370:        console.log("File selected:", file.name, file.type, file.size);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:432:          console.error("Error handling image upload:", error);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:473:            console.error("Invalid website URL not sent to API:", websiteUrl);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:489:        console.error("Error saving white label settings:", error);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:545:      console.log("Form not modified, skipping API call");
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:566:      console.error("Exception during API call:", error);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:642:                        console.log("Image loaded successfully");
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/WhiteLabelInfoStep.tsx:646:                        console.error(
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/PaymentMethodStep.tsx:71:      // console.log("Payment session created:", response);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelSteps/PaymentMethodStep.tsx:87:      console.error("Payment processing error:", error);
./src/app/(root)/audit/_/components/resultsBox/ResultPDFForm.tsx:39:          onChange={(e) => console.log(e)}
./src/app/(root)/audit/_/components/resultsBox/ResultPDFForm.tsx:47:          onChange={(e) => console.log(e)}
./src/app/(root)/audit/_/components/resultsBox/b.tsx:86:        console.error("Error fetching pricing data:", error);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelModal.tsx:235:        console.error("Error checking subscription status:", error);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelModal.tsx:284:        console.error("Error fetching white label settings:", error);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelModal.tsx:309:        console.error("Error fetching pricing data:", error);
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelModal.tsx:398:    console.log("handleDownloadRetryClick called"); // Debug log
./src/app/(root)/audit/_/components/resultsBox/WhiteLabelModal.tsx:401:    console.log("Current step set to select-plan"); // Debug log
./src/app/(root)/audit/_/components/resultsBox/SideAudit.tsx:135:          console.warn("PDF content may not be fully loaded, waiting...");
./src/app/(root)/audit/_/components/resultsBox/SideAudit.tsx:139:            console.warn("PDF content timeout, proceeding anyway");
./src/app/(root)/audit/_/components/resultsBox/SideAudit.tsx:160:      console.error("Print failed:", error);
./src/app/(root)/audit/_/components/resultsBox/links/Backlinks.tsx:248:    console.error("Error in Link component:", error);
./src/app/(root)/audit/_/components/pdf/ScreenshotSection.tsx:81:                    // console.error("Screenshot failed to load");
./src/services/httpService.ts:16:  console.log("HTTP Service initialized with baseURL:", baseURL);
./src/services/httpService.ts:90:    console.log(
./src/services/httpService.ts:118:      console.log(
./src/services/httpService.ts:126:    console.log("Starting token refresh process");
./src/services/httpService.ts:140:        console.log(
./src/services/httpService.ts:147:        console.error("Error during token refresh:", error);
./src/services/httpService.ts:166:      console.log(`API Success: ${res.config.url} - Status: ${res.status}`);
./src/services/httpService.ts:180:      console.error(`API Error: ${err.config?.url || "unknown endpoint"}`);
./src/services/httpService.ts:181:      console.error(`Status: ${err.response?.status || "No response"}`);
./src/services/httpService.ts:182:      console.error(`Message: ${err.message}`);
./src/services/httpService.ts:185:        console.error("Error details:", err.response.data);
./src/services/httpService.ts:207:          console.log(`Share API: Analysis not found for ${err.config.url}`);
./src/services/httpService.ts:235:            console.log("Token refresh in progress, adding request to queue");
./src/services/httpService.ts:249:            console.log("Attempting to refresh token for 401 response");
./src/services/httpService.ts:256:              console.log(
./src/services/httpService.ts:273:              console.log("Token refresh failed, clearing auth data");
./src/services/httpService.ts:283:                console.log(
./src/services/httpService.ts:296:            console.error("Error during token refresh:", refreshError);
./src/services/httpService.ts:306:              console.log(
./src/services/httpService.ts:323:            console.log(
./src/services/whiteLabelService.ts:38:    console.error("Error fetching white label settings:", error);
./src/services/whiteLabelService.ts:79:    console.error("Error saving white label settings:", error);
./src/services/whiteLabelService.ts:119:    console.error("Error updating white label settings:", error);
./src/services/storageService.ts:28:      console.error("Error setting localStorage item:", error);
./src/services/storageService.ts:42:      console.error("Error getting localStorage item:", error);
./src/services/storageService.ts:57:      console.error("Error removing localStorage item:", error);
./src/services/authService.ts:176:      console.log(
./src/services/authService.ts:180:      console.log("Response data structure:", Object.keys(response.data));
./src/services/authService.ts:185:        console.log(
./src/services/authService.ts:188:        console.log("User data from OTP verification response:", {
./src/services/authService.ts:196:        console.log("Access token saved to storage after OTP verification");
./src/services/authService.ts:200:        console.log("Refresh token saved to storage after OTP verification");
./src/services/authService.ts:212:        console.log("User data saved to storage after OTP verification");
./src/services/authService.ts:220:        console.log("Found tokens in nested structure after OTP verification");
./src/services/authService.ts:241:        console.log("Found legacy token format in OTP verification response");
./src/services/authService.ts:256:        console.warn(
./src/services/authService.ts:329:      console.log("Login successful with status code:", response.status);
./src/services/authService.ts:330:      console.log("Response data:", response.data);
./src/services/authService.ts:335:        console.log(
./src/services/authService.ts:338:        console.log("User data from login response:", {
./src/services/authService.ts:346:        console.log("Access token saved to storage");
./src/services/authService.ts:350:        console.log("Refresh token saved to storage");
./src/services/authService.ts:362:        console.log("User data saved to storage");
./src/services/authService.ts:370:        console.log("Found tokens in nested structure in login response");
./src/services/authService.ts:391:        console.log(
./src/services/authService.ts:414:        console.log("Found legacy token format");
./src/services/authService.ts:429:        console.warn(
./src/services/authService.ts:498:    console.log("No access token found in storage, user is not authenticated");
./src/services/authService.ts:502:  console.log("Access token found in storage, checking validity with server");
./src/services/authService.ts:515:      console.log("Authentication check successful, token is valid");
./src/services/authService.ts:520:        console.log("User data found in storage:", {
./src/services/authService.ts:526:        console.warn("Token is valid but no user data found in storage");
./src/services/authService.ts:529:      console.log("Authentication check failed, token is invalid");
./src/services/authService.ts:535:    console.error("Authentication check failed:", error);
./src/services/authService.ts:557:    console.error("No refresh token available");
./src/services/authService.ts:575:      console.log("Token refresh successful");
./src/services/authService.ts:587:      console.error("Token refresh failed:", response.data);
./src/services/authService.ts:591:    console.error("Error refreshing token:", error);
./src/services/profileService.ts:65:      console.warn("Profile API returned success but no data");
./src/services/profileService.ts:72:    console.error("Error fetching user profile:", error);
./src/services/profileService.ts:78:      console.error("Server error detail:", error.response.data.detail);
./src/services/profileService.ts:81:      console.error("Error message:", error.message);
./src/services/profileService.ts:125:    console.error("Error checking subscription:", error);
./src/services/profileService.ts:167:    console.error("Error checking Pro Plan subscription:", error);
./src/services/deviceService.ts:54:        console.log('Generated new device ID:', deviceId);
./src/services/deviceService.ts:63:      console.error('Error accessing device ID from localStorage:', error);
./src/services/paymentService.ts:84:      console.error("Error creating payment session:", error);
./src/services/paymentService.ts:87:        console.error(
./src/services/paymentService.ts:107:      console.error("Error checking payment status:", error);
./src/services/sessionService.ts:22:    console.error('Error getting audit count:', error);
./src/services/sessionService.ts:40:    console.error('Error incrementing audit count:', error);
./src/services/sessionService.ts:62:    console.error('Error resetting audit count:', error);
./src/services/analyticsService.ts:45:    console.log("Google Analytics and Google Ads initialized with consent");
./src/services/analyticsService.ts:47:    console.log("Google Analytics not initialized - no user consent");
./src/services/analyticsService.ts:95:  console.log("Google Analytics tracking disabled");
./src/services/analyticsService.ts:110:  console.log("Google Analytics tracking enabled");
./src/ui/charts/RadarChart.tsx:35:      // console.log("Adding interactive elements to radar chart");
./src/ui/charts/RadarChart.tsx:56:          // console.log(
./src/ui/charts/RadarChart.tsx:66:        // console.log(
./src/ui/charts/RadarChart.tsx:72:        console.warn("No text elements found in radar chart");
./src/ui/charts/RadarChart.tsx:97:            // console.log(`Label clicked: ${textContent}`);
./src/ui/charts/RadarChart.tsx:135:            // console.log(`Marker clicked: ${label}`);
./src/ui/charts/RadarChart.tsx:204:                // console.log(`Data point clicked: ${label}`);
./src/ui/charts/RadarChart.tsx:211:                // console.log(`Marker clicked: ${label}`);
./src/ui/charts/RadarChart.tsx:217:              // console.log("Chart mounted, adding label listeners");
./src/ui/charts/RadarChart.tsx:238:                      // console.log(`Text label clicked: ${textContent}`);
./src/ui/charts/RadarChart.tsx:342:      // console.log(`Container click detected on label: ${textContent}`);
./src/ui/charts/RadarChart.tsx:352:        // console.log(`Container click detected on parent label: ${parentText}`);
