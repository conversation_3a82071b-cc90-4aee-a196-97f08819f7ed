import { Metadata } from "next";
import { Suspense } from "react";
import { notFound, redirect } from "next/navigation";
import StaticBlogPageClient from "../../StaticBlogPageClient";
import blogService from "@/services/blogService";

// Static generation with ISR: revalidates every 5 minutes
export const revalidate = 300;

interface PageProps {
  params: Promise<{ page: string }>;
}

// Generate static params for pages 2 and onwards at build time
export async function generateStaticParams() {
  try {
    // Fetch first page to get total count
    const data = await blogService.getBlogPosts({ page: 1 });
    const totalPosts = data.count;
    const postsPerPage = 10;
    const totalPages = Math.ceil(totalPosts / postsPerPage);
    
    // Generate static params for pages 2-10 (page 1 is handled by /blog)
    const maxStaticPages = Math.min(totalPages, 10);
    const pages = [];
    
    for (let i = 2; i <= maxStaticPages; i++) {
      pages.push({ page: i.toString() });
    }
    
    return pages;
  } catch (error) {
    console.error("Error generating static params:", error);
    // Return at least page 2 if there's an error
    return [{ page: "2" }];
  }
}

// Generate metadata for each page
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { page } = await params;
  const pageNum = parseInt(page);
  
  if (isNaN(pageNum) || pageNum < 1) {
    return {
      title: "Page Not Found | SEO Analyser Blog",
      description: "The requested page could not be found.",
    };
  }

  const pageTitle = pageNum === 1 
    ? "SEO Analyser Blog | Expert SEO Tips, Guides & Industry Insights"
    : `SEO Analyser Blog - Page ${pageNum} | Expert SEO Tips & Guides`;
    
  const pageDescription = pageNum === 1
    ? "Discover expert SEO tips, comprehensive guides, and latest industry insights. Learn keyword research, on-page optimization, technical SEO, and digital marketing strategies to boost your website's rankings."
    : `Browse page ${pageNum} of our SEO blog for expert tips, comprehensive guides, and latest industry insights on search engine optimization.`;

  return {
    title: pageTitle,
    description: pageDescription,
    keywords: [
      "SEO Analyser",
      "SEO blog",
      "SEO tips",
      "SEO guides",
      "keyword research",
      "on-page SEO",
      "technical SEO",
      "backlink building",
      "local SEO",
      "SEO audit",
      "search engine optimization",
      "website optimization",
      "SEO strategies",
      "SEO best practices",
    ],
    openGraph: {
      title: pageTitle,
      description: pageDescription,
      type: "website",
      url: `https://seoanalyser.com.au/blog/page/${pageNum}`,
      siteName: "SEO Analyser",
      images: [
        {
          url: "https://seoanalyser.com.au/images/appLogo.svg",
          width: 1200,
          height: 630,
          alt: "SEO Analyser Blog - Expert SEO Tips and Guides",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      site: "@SEO_ANALYSER",
      creator: "@SEO_ANALYSER",
      title: pageTitle,
      description: pageDescription,
      images: ["https://seoanalyser.com.au/images/appLogo.svg"],
    },
    alternates: {
      canonical: `https://seoanalyser.com.au/blog/page/${pageNum}`,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

// Server component that renders the client component with static data
const BlogPageByNumber = async ({ params }: PageProps) => {
  const { page } = await params;
  const pageNum = parseInt(page);
  
  // Validate page number
  if (isNaN(pageNum) || pageNum < 1) {
    notFound();
  }
  
  // Page 1 should be handled by /blog, redirect if someone tries to access /blog/page/1
  if (pageNum === 1) {
    redirect('/blog');
  }

  try {
    // Fetch the specific page data
    const data = await blogService.getBlogPosts({ page: pageNum }, 300);
    
    // If page number is too high, return 404
    const totalPages = Math.ceil(data.count / 10);
    if (pageNum > totalPages && data.count > 0) {
      notFound();
    }
    
    const initialPosts = (data.results || []).map((post) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      author: post.author,
      snippet: post.snippet || "",
      publish_timestamp: post.publish_timestamp,
      url: post.url,
      tags: post.tags,
      cover_image: post.cover_image ?? null,
    }));

    const initialCategories = data.categories || [];
    const initialCount = data.count;
    const initialNextPageUrl = data.next;
    const initialPreviousPageUrl = data.previous;

    return (
      <Suspense fallback={null}>
        <StaticBlogPageClient
          initialPosts={initialPosts}
          initialCategories={initialCategories}
          initialCount={initialCount}
          initialNextPageUrl={initialNextPageUrl}
          initialPreviousPageUrl={initialPreviousPageUrl}
          initialPage={pageNum}
        />
      </Suspense>
    );
  } catch (error) {
    console.error(`Error fetching blog page ${pageNum}:`, error);
    // If server fetch fails, return 404 for specific pages
    notFound();
  }
};

export default BlogPageByNumber;