"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { useBlogStore } from "@/store/blogStore";
import BannerScroll from "./components/BannerScroll";
import BlogCard, { BlogCardProps } from "./components/BlogCard";
import SearchInput from "./components/SearchInput";
import CategoryList from "./components/CategoryList";
import { Pagination } from "./components/Pagination";
import BlogCardSkeleton from "./components/BlogCardSkeleton";
import BannerScrollSkeleton from "./components/BannerScrollSkeleton";
import http from "@/services/httpService";
import Link from "next/link";

// Define the API response interface
interface SearchResponse {
  query: string;
  results: SearchResult[];
  count: number;
  categories: Category[];
}

interface Category {
  name: string;
  slug: string;
}

interface SearchResult {
  id: number;
  title: string;
  slug: string;
  category: {
    name: string;
    slug: string;
  };
  author: string;
  publish_timestamp: number;
  snippet: string;
  body?: string;
  cover_image?: string;
  tags: string[];
  url: string;
}

export interface StaticBlogPageClientProps {
  initialPosts: BlogCardProps[];
  initialCategories: Category[];
  initialCount: number;
  initialNextPageUrl?: string | null;
  initialPreviousPageUrl?: string | null;
  initialPage: number;
}

// Separate component for search params to wrap in Suspense
const StaticBlogPageWithSearchParams = ({
  initialPosts,
  initialCategories,
  initialCount,
  initialNextPageUrl,
  initialPreviousPageUrl,
  initialPage,
}: StaticBlogPageClientProps) => {
  const searchParams = useSearchParams();
  const { searchQuery } = useBlogStore();

  const [displayPosts, setDisplayPosts] = useState<BlogCardProps[]>(initialPosts);
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(() => {
    const itemsPerPage = 10;
    return Math.ceil(initialCount / itemsPerPage);
  });
  const [nextPageUrl, setNextPageUrl] = useState<string | null>(initialNextPageUrl ?? null);
  const [previousPageUrl, setPreviousPageUrl] = useState<string | null>(initialPreviousPageUrl ?? null);
  const [dataCount, setDataCount] = useState(initialCount);
  const [isSearchError, setIsSearchError] = useState(false);
  const [isSearchMode, setIsSearchMode] = useState(false);

  // Handle search functionality
  useEffect(() => {
    const handleSearch = async () => {
      if (!searchQuery) {
        // Reset to static data when search is cleared
        setDisplayPosts(initialPosts);
        setCategories(initialCategories);
        setCurrentPage(initialPage);
        setTotalPages(Math.ceil(initialCount / 10));
        setNextPageUrl(initialNextPageUrl ?? null);
        setPreviousPageUrl(initialPreviousPageUrl ?? null);
        setDataCount(initialCount);
        setIsSearchMode(false);
        setIsSearchError(false);
        return;
      }

      setLoading(true);
      setError(null);
      setIsSearchMode(true);

      try {
        const searchUrl = `/api/blog/search/?q=${encodeURIComponent(searchQuery)}`;
        const response = await http.get(searchUrl, { useAuth: false });
        const data = response.data as SearchResponse;

        // Map search results
        const searchResults = (data.results || []).map((post) => ({
          id: post.id,
          title: post.title,
          slug: post.slug,
          author: post.author,
          snippet: post.snippet,
          publish_timestamp: post.publish_timestamp,
          url: post.url,
          tags: post.tags || [],
          category: post.category,
          cover_image: post.cover_image ?? null,
        }));

        setDisplayPosts(searchResults);
        setCategories(data.categories || []);
        setDataCount(data.count);
        setCurrentPage(1);
        setTotalPages(Math.ceil(data.count / 10));
        setNextPageUrl(null);
        setPreviousPageUrl(null);
        setIsSearchError(false);
      } catch (searchErr) {
        console.error("Error in search API call:", searchErr);
        setIsSearchError(true);
        // Keep showing static data on search error
        setDisplayPosts(initialPosts);
        setCategories(initialCategories);
        setDataCount(initialCount);
      } finally {
        setLoading(false);
      }
    };

    handleSearch();
  }, [searchQuery, initialPosts, initialCategories, initialCount, initialNextPageUrl, initialPreviousPageUrl, initialPage]);

  return (
    <div className="w-full mt-8 lg:mt-[84px] container">
      <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
        <div className="col-span-1 flex flex-col gap-4">
          <SearchInput />
          <CategoryList categories={categories} currentCategory={null} />
        </div>
        <div className="col-span-3 relative">
          {/* Loading State for Search */}
          {loading && (
            <>
              {/* Show search results title skeleton if searching */}
              {searchQuery && (
                <div className="mb-6">
                  <div className="h-8 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 bg-[length:200%_100%] animate-[shimmer_2s_infinite] rounded w-64 mb-2"></div>
                  <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 bg-[length:200%_100%] animate-[shimmer_2s_infinite] rounded w-32"></div>
                </div>
              )}

              {/* Show banner skeleton only if not searching */}
              {!searchQuery && <BannerScrollSkeleton />}

              {/* Blog cards skeleton */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
                {Array.from({ length: 6 }).map((_, index) => (
                  <BlogCardSkeleton key={index} />
                ))}
              </div>
            </>
          )}

          {/* Error State */}
          {error && !loading && (
            <div className="text-center py-18 bg-gray-200 border border-gray-300 rounded-lg">
              <h2 className="text-xl font-bold text-gray-700 mb-2">
                Unable to Load Blog Posts
              </h2>
              <p className="text-sm text-gray-500">{error}</p>
              <Link
                href="/blog"
                className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-opacity-90 transition-all inline-block"
              >
                Try Again
              </Link>
            </div>
          )}

          {/* Content - show when not loading and no error */}
          {!loading && !error && (
            <>
              {/* Show search results title if searching */}
              {searchQuery && (
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-secondary">
                    Search results for "{searchQuery}"
                  </h2>
                  <p className="text-gray-500 mt-2">
                    Found {dataCount} {dataCount === 1 ? "result" : "results"}
                    {isSearchError && (
                      <span className="text-red-500 text-sm ml-2">
                        (Search API error - showing regular posts instead)
                      </span>
                    )}
                  </p>
                </div>
              )}

              {/* Only show banner if not searching and have enough posts */}
              {!searchQuery && displayPosts.length >= 3 && (
                <BannerScroll blogData={displayPosts.slice(0, 3)} />
              )}

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
                {displayPosts.length > 0 ? (
                  displayPosts.map((post) => (
                    <BlogCard key={post.id} {...post} />
                  ))
                ) : (
                  <div className="col-span-2 py-10 text-center">
                    <h3 className="text-xl font-semibold text-secondary">
                      No posts found
                    </h3>
                    <p className="mt-2 text-gray-500">
                      Try a different search term
                    </p>
                  </div>
                )}
              </div>

              {/* Show pagination only when not in search mode */}
              {!isSearchMode && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  nextPageUrl={nextPageUrl}
                  previousPageUrl={previousPageUrl}
                />
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

// Main export component with Suspense wrapper
const StaticBlogPageClient = (props: StaticBlogPageClientProps) => {
  return (
    <Suspense fallback={
      <div className="w-full mt-8 lg:mt-[84px] mb-12 container">
        <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
          <div className="col-span-1 flex flex-col gap-4">
            <div className="h-10 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 bg-[length:200%_100%] animate-[shimmer_2s_infinite] rounded"></div>
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="h-8 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 bg-[length:200%_100%] animate-[shimmer_2s_infinite] rounded"></div>
              ))}
            </div>
          </div>
          <div className="col-span-3">
            <BannerScrollSkeleton />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
              {Array.from({ length: 6 }).map((_, index) => (
                <BlogCardSkeleton key={index} />
              ))}
            </div>
          </div>
        </div>
      </div>
    }>
      <StaticBlogPageWithSearchParams {...props} />
    </Suspense>
  );
};

export default StaticBlogPageClient;