"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import AuthModal from "@/components/auth/AuthModal";
import { useAuthStore } from "@/store/authStore";
import authErrorHandler from "@/utils/authErrorHandler";

export default function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient());
  const fetchProfile = useAuthStore((state) => state.fetchProfile);
  const openAuthModal = useAuthStore((state) => state.openAuthModal);

  // Check authentication status on initial load only if the page is visible
  useEffect(() => {
    // Defer auth check to improve initial page load performance
    const deferredAuthCheck = () => {
      if (
        typeof document !== "undefined" &&
        document.visibilityState === "visible"
      ) {
        console.log(
          "Page is visible, performing deferred auth check with server validation"
        );
        // Use cached auth for initial load to improve performance
        fetchProfile(false);
      }
    };

    // Defer auth check by 100ms to allow critical rendering to complete
    const timeoutId = setTimeout(deferredAuthCheck, 100);

    // Set up visibility change listener to check auth when page becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        console.log("Page became visible, checking auth status");
        // Use cached auth for visibility changes (not forced)
        fetchProfile();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [fetchProfile]);

  // Initialize auth error handler with debouncing
  useEffect(() => {
    // Track if we've already shown an auth modal due to an error
    let authModalShownTimestamp = 0;
    const AUTH_MODAL_DEBOUNCE = 10000; // 10 seconds

    // Set up auth error handler to open auth modal on 401 errors
    const handleAuthError = () => {
      const now = Date.now();

      // Only show the auth modal if we haven't shown one recently
      if (now - authModalShownTimestamp > AUTH_MODAL_DEBOUNCE) {
        console.log("Auth error detected in Providers, opening auth modal");
        authModalShownTimestamp = now;
        openAuthModal("login-register", "general", null, false);
      } else {
        console.log(
          "Auth error detected, but modal was recently shown. Ignoring."
        );
      }
    };

    // Register the handler
    authErrorHandler.addAuthErrorListener(handleAuthError);

    // Clean up on unmount
    return () => {
      authErrorHandler.removeAuthErrorListener(handleAuthError);
    };
  }, [openAuthModal]);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <AuthModal />
    </QueryClientProvider>
  );
}
