"use client";
import { useCallback } from "react";
import { DownloadIcon } from "@/ui/icons/general";
import { CrossSmallIcon } from "@/ui/icons/general/CrossSmallIcon";
import { PaymentStatusResponse } from "@/services/paymentService";
import PaymentReceipt from "@/components/payment/PaymentReceipt";
import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";

// Define the new pricing plan interface based on the updated API response
interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: string;
  interval_count: number;
  product_id: string;
  metadata: Record<string, any>;
  price_metadata: Record<string, any>;
}

// Define types for billing period
type BillingPeriod = "monthly" | "annually";

// Define types for the White Label form data
interface WhiteLabelFormData {
  brandName: string;
  logoImage: File | null;
  phoneNumber: string;
  website: string;
  countryCode?: string;
}

interface DownloadStepProps {
  onComplete?: () => void; // Made optional since we're not using it anymore
  onBack?: () => void; // Add callback for back navigation
  onRetry?: () => void; // Add callback for retry functionality
  onClose?: () => void; // Add callback for closing the modal
  formData: WhiteLabelFormData;
  selectedPlan: string;
  billingPeriod: BillingPeriod;
  apiPricingData: PricingPlan[];
  logoPreviewUrl: string | null;
  paymentStatus?: PaymentStatusResponse | null;
  isLoadingPayment?: boolean;
  taskId?: string;
  paymentId?: string;
  hasActiveSubscription?: boolean;
  hasActiveProPlan?: boolean; // Pro Plan subscription status
  profileData?: any; // Profile API data with subscriptions
}

// Props for the ActiveSubscriptionView component
interface ActiveSubscriptionViewProps {
  formData: WhiteLabelFormData;
  logoPreviewUrl: string | null;
  taskId?: string;
  paymentId?: string;
  whitelabelSetting?: any;
  hasActiveProPlan?: boolean; // Pro Plan subscription status
  profileData?: any; // Profile API data with subscriptions
}

// Component for users with active subscriptions - clean, minimal design
function ActiveSubscriptionView({
  formData,
  logoPreviewUrl,
  taskId,
  paymentId,
  whitelabelSetting,
  hasActiveProPlan = false,
  profileData,
}: ActiveSubscriptionViewProps) {
  return (
    <div className="overflow-y-auto">
      {/* Only show PaymentReceipt if we don't have profile data with subscriptions */}
      {!profileData?.subscriptions?.length && (
        <PaymentReceipt
          paymentStatus={{
            plan: "Pro Plan",
            plan_period: "Active Subscription",
            plan_details: {
              price: "Active Subscription",
              name: "Pro Plan",
              period: "Active",
            },
            amount: "Active Subscription",
            payment_status: "Active",
            data: { created_at: new Date().toISOString() },
            profile: null,
            whitelabel_setting: whitelabelSetting || null,
            expire_date: "N/A",
          }}
          transactionId={paymentId}
          returnHome={false}
          currency="usd"
        />
      )}

      {/* White Label Settings Summary - Main content for users with active subscriptions */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-lg mt-5 max-w-3xl mx-auto mb-10">
        <div className="w-full flex justify-between items-center p-5 border-b border-gray-100">
          <span className="font-medium text-secondary text-lg">
            White Label Settings
          </span>
          {hasActiveProPlan && (
            <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
              Pro Plan
            </div>
          )}
        </div>

        <div className="p-2">
          {/* Brand logo showcase */}
          {(logoPreviewUrl || whitelabelSetting?.logo) && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="flex justify-center my-5"
            >
              <div className="h-12 w-12 rounded-lg overflow-hidden bg-white p-3 border border-gray-200 shadow-sm flex items-center justify-center relative">
                <Image
                  src={whitelabelSetting?.logo || logoPreviewUrl || ""}
                  alt="Brand Logo"
                  className="object-contain"
                  fill
                  style={{ objectFit: "contain" }}
                />
              </div>
            </motion.div>
          )}

          {/* Brand Name Row */}
          <div className="flex justify-between items-center p-5 hover:bg-gray-50 rounded-md">
            <div className="text-secondary">Brand Name</div>
            <div className="text-secondary font-medium">
              {whitelabelSetting?.brand_name || formData.brandName}
            </div>
          </div>

          {/* Phone Number Row - Only shown if available */}
          {(whitelabelSetting?.phone_number || formData.phoneNumber) && (
            <div className="flex justify-between items-center p-5 hover:bg-gray-50 rounded-md">
              <div className="text-secondary">Phone Number</div>
              <div className="text-secondary font-medium">
                {whitelabelSetting?.phone_number ||
                  (formData.countryCode && formData.phoneNumber
                    ? `${formData.countryCode}${formData.phoneNumber}`
                    : formData.phoneNumber)}
              </div>
            </div>
          )}

          {/* Website Row */}
          <div className="flex justify-between items-center p-5 hover:bg-gray-50 rounded-md border-t border-gray-200 mt-1 bg-gray-50">
            <div className="text-secondary">Website</div>
            <div className="text-secondary font-medium">
              {whitelabelSetting?.website || formData.website}
            </div>
          </div>
        </div>
      </div>

      {/* Spacer to ensure content doesn't get hidden behind fixed button */}
      <div className="h-24 sm:h-28"></div>

      {/* Fixed Download Button Section */}
      <div className="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-4 sm:p-5 z-10">
        <div className="max-w-3xl mx-auto flex flex-col items-center">
          <p className="text-secondary text-sm sm:text-base text-center mb-3 sm:mb-4">
            Your Pro Plan PDF is now ready. Click the button below to download.
          </p>
          <motion.div
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.98 }}
            className="w-full sm:w-auto"
          >
            <Link
              href={`/white-label-pdf?share=${taskId || ""}&uid=${
                paymentId || ""
              }`}
              className="btn btn--primary px-6 sm:px-10 py-3 sm:py-4 text-sm sm:text-base inline-flex items-center justify-center shadow-md w-full sm:w-auto rounded-lg"
            >
              <DownloadIcon className="w-5 h-5 sm:w-6 sm:h-6 mr-2" />
              Download White Label PDF
            </Link>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

// Refresh/Retry Icon Component
const RefreshIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C9.25 4 6.82 5.38 5.5 7.5M5.5 7.5V4M5.5 7.5H9"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default function DownloadStep({
  onBack,
  onRetry,
  onClose,
  formData,
  selectedPlan,
  billingPeriod,
  apiPricingData,
  logoPreviewUrl,
  paymentStatus,
  isLoadingPayment,
  taskId,
  paymentId,
  hasActiveSubscription = false,
  hasActiveProPlan = false,
  profileData,
}: DownloadStepProps) {
  // No need for transaction ID state as we'll use paymentId directly

  // Get price information based on selected plan and billing period
  const getPriceInfo = useCallback(() => {
    // Get the appropriate pricing data
    const intervalType = billingPeriod === "monthly" ? "month" : "year";

    // Find the plan with the matching interval
    const plan = apiPricingData.find((p) => p.interval === intervalType);

    if (!plan) {
      // Fallback to default pricing if API data is not available
      const defaultPrice = billingPeriod === "monthly" ? 28 : 288;

      return billingPeriod === "annually"
        ? `$${(defaultPrice / 12).toFixed(
            2
          )}/month (billed annually as $${defaultPrice.toFixed(2)})`
        : `$${defaultPrice.toFixed(2)}/month`;
    }

    // Use the price from the API data
    const price = plan.price;

    return billingPeriod === "annually"
      ? `$${(price / 12).toFixed(2)}/month (billed annually as $${price.toFixed(
          2
        )})`
      : `$${price.toFixed(2)}/month`;
  }, [billingPeriod, apiPricingData]);

  // Extract just the numeric amount from the price info
  const extractAmount = useCallback(() => {
    const priceInfo = paymentStatus?.amount || getPriceInfo();
    // Extract the dollar amount (either the monthly or annual amount)
    const match = priceInfo.match(/\$(\d+(\.\d+)?)/);
    return match ? match[0] : priceInfo;
  }, [paymentStatus, getPriceInfo]);

  // Check if payment status indicates a non-success state
  const isPaymentFailed = useCallback(() => {
    if (!paymentStatus) return false;

    const status =
      paymentStatus.payment_status?.toLowerCase() ||
      paymentStatus.status?.toLowerCase();
    return (
      status === "failed" ||
      status === "canceled" ||
      status === "cancelled" ||
      status === "error"
    );
  }, [paymentStatus]);

  // Handle retry button click - now goes to pricing step
  const handleRetryClick = useCallback(() => {
    console.log("Retry button clicked"); // Debug log
    if (onRetry) {
      console.log("Calling onRetry callback"); // Debug log
      // Use the callback if provided (preferred method) - this will go to pricing step
      onRetry();
    } else {
      console.log("No onRetry callback, using fallback"); // Debug log
      // Fallback: redirect to current URL without payment ID
      if (typeof window !== "undefined") {
        const url = new URL(window.location.href);
        url.searchParams.delete("uid");
        window.location.href = url.pathname + url.search;
      }
    }
  }, [onRetry]);

  // Handle back button click
  const handleBackClick = useCallback(() => {
    if (onBack) {
      // Use the callback if provided (preferred method)
      onBack();
    } else {
      // Fallback: redirect to pricing page
      if (typeof window !== "undefined") {
        window.location.href = "/pricing";
      }
    }
  }, [onBack]);

  // Get appropriate error message based on payment status
  const getErrorMessage = useCallback(() => {
    if (!paymentStatus)
      return "Your payment could not be processed. Please try again.";

    const status =
      paymentStatus.payment_status?.toLowerCase() ||
      paymentStatus.status?.toLowerCase();

    switch (status) {
      case "canceled":
      case "cancelled":
        return "Your payment was canceled. You can try again or choose a different payment method.";
      case "failed":
        return "Your payment failed. Please check your payment details and try again.";
      case "error":
        return "An error occurred while processing your payment. Please try again.";
      default:
        return (
          paymentStatus.message ||
          "Your payment could not be processed. Please try again."
        );
    }
  }, [paymentStatus]);

  // Get appropriate error title based on payment status
  const getErrorTitle = useCallback(() => {
    if (!paymentStatus) return "Payment Failed";

    const status =
      paymentStatus.payment_status?.toLowerCase() ||
      paymentStatus.status?.toLowerCase();

    switch (status) {
      case "canceled":
      case "cancelled":
        return "Payment Canceled";
      case "failed":
        return "Payment Failed";
      case "error":
        return "Payment Error";
      default:
        return "Payment Failed";
    }
  }, [paymentStatus]);

  if (isLoadingPayment) {
    return (
      <div className="overflow-y-auto max-w-3xl mx-auto">
        <div className="bg-white p-6 my-5 border border-gray-200 rounded-xl shadow-lg">
          <div className="flex items-center justify-center">
            <div className="w-8 h-8 sm:w-10 sm:h-10 border-3 border-primary border-t-transparent rounded-full animate-spin mr-3 sm:mr-4"></div>
            <div>
              <h3 className="text-secondary font-bold text-lg sm:text-xl">
                Verifying Payment
              </h3>
              <p className="text-gray-600 text-sm sm:text-base">
                Please wait while we process your transaction...
              </p>
            </div>
          </div>
          <div className="mt-6 w-full bg-gray-200 h-2 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-primary rounded-full"
              initial={{ width: "10%" }}
              animate={{ width: "90%" }}
              transition={{ duration: 3, ease: "easeInOut" }}
            />
          </div>
        </div>
      </div>
    );
  }

  // Show error state if payment failed
  if (isPaymentFailed()) {
    return (
      <div className="overflow-y-auto max-w-3xl mx-auto">
        <div className="bg-white p-6 my-5 border border-red-200 rounded-xl shadow-lg">
          <div className="flex flex-col items-center text-center mb-6">
            <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mb-4">
              <svg
                className="w-8 h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
            <h3 className="text-red-600 font-bold text-xl sm:text-2xl mb-2">
              {getErrorTitle()}
            </h3>
            <p className="text-gray-600 text-base sm:text-lg max-w-md">
              {getErrorMessage()}
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={handleRetryClick}
              className="btn btn--primary px-6 py-3 text-base font-medium flex items-center gap-2"
            >
              <RefreshIcon className="w-5 h-5" />
              Try Again
            </button>
            <button
              onClick={onClose}
              className="btn btn--secondary px-6 py-3 text-base font-medium flex items-center gap-2"
            >
              <CrossSmallIcon className="w-5 h-5" />
              Close
            </button>
          </div>

          {/* Additional help text */}
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500 mb-2">
              You can close this modal if you don't want to purchase now.
            </p>
            <p className="text-sm text-gray-500">
              Need help? Contact our support team at{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-primary hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    );
  }

  // For users with active subscriptions, show the simplified view
  if (hasActiveSubscription && !paymentStatus) {
    return (
      <ActiveSubscriptionView
        formData={formData}
        logoPreviewUrl={logoPreviewUrl}
        taskId={taskId}
        paymentId={paymentId}
        whitelabelSetting={null}
        hasActiveProPlan={hasActiveProPlan}
        profileData={profileData}
      />
    );
  }

  // For users who just completed payment, show the receipt-style display
  return (
    <div className="overflow-y-auto">
      {/* Only show PaymentReceipt if we don't have profile data with active subscriptions */}
      {!profileData?.subscriptions?.length && (
        <PaymentReceipt
          paymentStatus={
            paymentStatus || {
              plan:
                selectedPlan === "basic" ? "Pro Plan" : "Pro Plan & Embedding",
              plan_period: billingPeriod,
              plan_details: {
                price: extractAmount(),
                name:
                  selectedPlan === "basic"
                    ? "Pro Plan"
                    : "Pro Plan & Embedding",
                period: billingPeriod,
              },
              amount: extractAmount(),
              payment_status: "Completed",
              data: { created_at: new Date().toISOString() },
              profile: null,
              whitelabel_setting: null,
              expire_date: "N/A",
            }
          }
          transactionId={paymentId}
          returnHome={false}
          currency={
            apiPricingData.length > 0 ? apiPricingData[0].currency : "usd"
          }
        />
      )}

      {/* White Label Settings Section - Main content when user has active subscription */}
      <div className="bg-white rounded-xl shadow-lg overflow-hidden mt-4">
        <div className="w-full flex justify-between items-center p-5 border-b border-gray-100">
          <span className="font-medium text-secondary text-lg">
            White Label Settings
          </span>
          {hasActiveProPlan && (
            <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
              Pro Plan
            </div>
          )}
        </div>

        <div className="p-2">
          {/* Brand logo showcase */}
          {(logoPreviewUrl || paymentStatus?.whitelabel_setting?.logo) && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="flex justify-center my-5"
            >
              <div className="h-12 w-12 rounded-lg overflow-hidden bg-white p-3 border border-gray-200 shadow-sm flex items-center justify-center relative">
                <Image
                  src={
                    paymentStatus?.whitelabel_setting?.logo ||
                    logoPreviewUrl ||
                    ""
                  }
                  alt="Brand Logo"
                  className="object-contain"
                  fill
                  style={{ objectFit: "contain" }}
                />
              </div>
            </motion.div>
          )}

          {/* Brand Name Row */}
          <div className="flex justify-between items-center p-5 hover:bg-gray-50 rounded-md">
            <div className="text-secondary">Brand Name</div>
            <div className="text-secondary font-medium">
              {paymentStatus?.whitelabel_setting?.brand_name ||
                formData.brandName}
            </div>
          </div>

          {/* Phone Number Row - Only shown if available */}
          {(paymentStatus?.whitelabel_setting?.phone_number ||
            formData.phoneNumber) && (
            <div className="flex justify-between items-center p-5 hover:bg-gray-50 rounded-md">
              <div className="text-secondary">Phone Number</div>
              <div className="text-secondary font-medium">
                {paymentStatus?.whitelabel_setting?.phone_number ||
                  (formData.countryCode && formData.phoneNumber
                    ? `${formData.countryCode}${formData.phoneNumber}`
                    : formData.phoneNumber)}
              </div>
            </div>
          )}

          {/* Website Row */}
          <div className="flex justify-between items-center p-5 hover:bg-gray-50 rounded-md border-t border-gray-200 mt-1 bg-gray-50">
            <div className="text-secondary">Website</div>
            <div className="text-secondary font-medium">
              {paymentStatus?.whitelabel_setting?.website || formData.website}
            </div>
          </div>
        </div>
      </div>

      {/* Spacer to ensure content doesn't get hidden behind fixed button */}
      <div className="h-24 sm:h-28"></div>

      {/* Fixed Download Button Section */}
      <div className="fixed bottom-0 left-0 right-0 bg-white shadow-md border-t border-gray-300 p-3 sm:p-4 z-10">
        <div className="max-w-3xl mx-auto flex flex-col items-center">
          <p className="text-secondary text-sm sm:text-base text-center mb-3 sm:mb-4">
            Your Pro Plan PDF is now ready. Click the button below to download.
          </p>
          <motion.div
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.98 }}
            className="w-full sm:w-auto"
          >
            <Link
              href={`/white-label-pdf?share=${taskId || ""}&uid=${
                paymentId || ""
              }`}
              className="btn btn--primary px-6 sm:px-10 py-3 sm:py-4 text-sm sm:text-base inline-flex items-center justify-center shadow-md w-full sm:w-auto rounded-lg"
            >
              <DownloadIcon className="w-5 h-5 sm:w-6 sm:h-6 mr-2" />
              Download White Label PDF
            </Link>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
