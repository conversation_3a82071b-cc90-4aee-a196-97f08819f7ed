"use client";
import { useState, useCallback, useEffect } from "react";
import whiteLabelService from "@/services/whiteLabelService";
import CountrySelector, { CountryData, countries } from "./CountrySelector";

// Define types for the White Label form data
interface WhiteLabelFormData {
  brandName: string;
  logoImage: File | null;
  phoneNumber: string;
  website: string;
  countryCode?: string;
}

// Define types for form validation errors
interface ValidationErrors {
  brandName: string;
  phoneNumber: string;
  website: string;
  logoImage: string;
}

// Define types for API response
interface ApiResponse {
  success: boolean;
  data?: any;
  error?: string;
}

interface WhiteLabelInfoStepProps {
  onNext: (formData: WhiteLabelFormData) => void;
  initialFormData: WhiteLabelFormData;
  hasExistingSettings?: boolean;
  logoPreviewUrl?: string | null;
  isLoading?: boolean;
}

export default function WhiteLabelInfoStep({
  onNext,
  initialFormData,
  hasExistingSettings = false,
  logoPreviewUrl: externalLogoUrl = null,
  isLoading: externalLoading = false,
}: WhiteLabelInfoStepProps) {
  // State for the form data
  const [formData, setFormData] = useState<WhiteLabelFormData>(initialFormData);

  // State for selected country
  const [selectedCountry, setSelectedCountry] = useState<CountryData>(() => {
    // If countryCode is provided in initialFormData, use it
    if (initialFormData.countryCode) {
      const country = countries.find(
        (c) => c.dialCode === initialFormData.countryCode
      );
      if (country) return country;
    }

    // Default to US
    return countries.find((c) => c.code === "US") || countries[0];
  });

  // State for form input values (to prevent lag during typing)
  const [inputValues, setInputValues] = useState({
    brandName: initialFormData.brandName || "",
    phoneNumber: initialFormData.phoneNumber || "",
    website: initialFormData.website || "",
  });

  // State for field validation errors
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({
    brandName: "",
    phoneNumber: "",
    website: "",
    logoImage: "",
  });

  // State for tracking if fields have been touched (for validation)
  const [touchedFields, setTouchedFields] = useState({
    brandName: false,
    phoneNumber: false,
    website: false,
    logoImage: false,
  });

  // State for loading
  const [isLoading, setIsLoading] = useState(externalLoading);

  // State for errors
  const [error, setError] = useState<string | null>(null);

  // State for logo preview data URL
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(
    externalLogoUrl
  );

  // State to track if form has been modified
  const [isFormModified, setIsFormModified] = useState<boolean>(false);

  // State for image preview loading/error
  const [imagePreviewStatus, setImagePreviewStatus] = useState<
    "loading" | "loaded" | "error"
  >("loading");

  // Effect to create preview URL for logo if it exists
  useEffect(() => {
    // If we already have an external logo URL from props, use that
    if (externalLogoUrl) {
      setLogoPreviewUrl(externalLogoUrl);
      setImagePreviewStatus("loaded");
      return;
    }

    // Otherwise, if we have a logo file, create a preview
    if (initialFormData.logoImage) {
      setImagePreviewStatus("loading");

      // Use FileReader to create a data URL instead of a blob URL
      const reader = new FileReader();

      reader.onload = (e) => {
        if (e.target?.result) {
          setLogoPreviewUrl(e.target.result as string);
        }
      };

      reader.onerror = () => {
        console.error("Error reading file for preview");
        setImagePreviewStatus("error");
      };

      reader.readAsDataURL(initialFormData.logoImage);
    }
  }, [initialFormData.logoImage, externalLogoUrl]);

  // No need for cleanup with data URLs
  useEffect(() => {
    return () => {
      // Data URLs are garbage collected automatically
      // No need to revoke them like blob URLs
    };
  }, []);

  // Update selected country when initialFormData changes
  useEffect(() => {
    if (initialFormData.countryCode) {
      const country = countries.find(
        (c) => c.dialCode === initialFormData.countryCode
      );
      if (country) {
        setSelectedCountry(country);
      }
    }
  }, [initialFormData.countryCode]);

  // Reset form modification state when initialFormData changes
  useEffect(() => {
    // Reset input values to match initialFormData
    setInputValues({
      brandName: initialFormData.brandName || "",
      phoneNumber: initialFormData.phoneNumber || "",
      website: initialFormData.website || "",
    });

    // Reset form modification state
    setIsFormModified(false);
  }, [initialFormData]);

  // Validation functions
  const validateBrandName = useCallback((value: string): string => {
    if (!value.trim()) {
      return "Brand name is required";
    }
    return "";
  }, []);

  const validatePhoneNumber = useCallback((value: string): string => {
    // Phone number is not required, so don't validate if empty
    if (!value.trim()) {
      return "";
    }
    if (!/^\d+$/.test(value)) {
      return "Phone number must contain only digits";
    }
    return "";
  }, []);

  const validateWebsite = useCallback((value: string): string => {
    // Website is not required, so don't validate if empty
    if (!value.trim()) {
      return "";
    }

    // Comprehensive URL validation with https requirement
    try {
      const url = new URL(value);
      if (url.protocol !== "https:") {
        return "URL must start with https://";
      }
      return "";
    } catch (e) {
      // If URL constructor throws an error, it's not a valid URL
      return "Please enter a valid website URL (e.g., https://example.com)";
    }
  }, []);

  // Handle form input changes with validation
  const handleInputChange = useCallback(
    (field: keyof WhiteLabelFormData, value: string) => {
      // Update the input values state during typing
      setInputValues((prev) => ({
        ...prev,
        [field]: value,
      }));

      // Mark field as touched
      setTouchedFields((prev) => ({
        ...prev,
        [field]: true,
      }));

      // Validate the field based on its type
      let errorMessage = "";
      switch (field) {
        case "brandName":
          errorMessage = validateBrandName(value);
          break;
        case "phoneNumber":
          errorMessage = validatePhoneNumber(value);
          break;
        case "website":
          errorMessage = validateWebsite(value);
          break;
      }

      // Update validation errors
      setValidationErrors((prev) => ({
        ...prev,
        [field]: errorMessage,
      }));

      // Check if form has been modified by comparing with initial data
      const isModified =
        field === "brandName"
          ? value !== initialFormData.brandName
          : field === "phoneNumber"
          ? value !== initialFormData.phoneNumber
          : field === "website"
          ? value !== initialFormData.website
          : false;

      if (isModified) {
        setIsFormModified(true);
      } else {
        // Check if other fields are modified
        const otherFieldsModified =
          (field !== "brandName" &&
            inputValues.brandName !== initialFormData.brandName) ||
          (field !== "phoneNumber" &&
            inputValues.phoneNumber !== initialFormData.phoneNumber) ||
          (field !== "website" &&
            inputValues.website !== initialFormData.website) ||
          formData.logoImage !== initialFormData.logoImage;

        setIsFormModified(otherFieldsModified);
      }
    },
    [
      validateBrandName,
      validatePhoneNumber,
      validateWebsite,
      initialFormData,
      inputValues,
      formData.logoImage,
    ]
  );

  // Helper function to validate image dimensions
  const validateImageDimensions = useCallback(
    (file: File): Promise<{ isValid: boolean; message: string }> => {
      return new Promise((resolve) => {
        // First check if the file is a valid PNG
        if (file.type !== "image/png") {
          resolve({
            isValid: false,
            message: "Please upload a PNG image file only",
          });
          return;
        }

        // Create a FileReader to read the file
        const reader = new FileReader();

        reader.onload = (event) => {
          if (!event.target?.result) {
            resolve({
              isValid: false,
              message: "Failed to read image file",
            });
            return;
          }

          const dataUrl = event.target.result as string;
          const img = new Image();

          // Set up a timeout to handle cases where the image might hang
          const timeoutId = setTimeout(() => {
            console.warn("Image validation timed out");
            resolve({
              isValid: false,
              message: "Image validation timed out. Please try another image.",
            });
          }, 5000); // 5 second timeout

          img.onload = () => {
            clearTimeout(timeoutId);
            console.log(
              `Image loaded with dimensions: ${img.width}x${img.height}`
            );

            // Check if image dimensions exceed 1000x1000 pixels
            if (img.width > 1000 || img.height > 1000) {
              resolve({
                isValid: false,
                message: `Image dimensions (${img.width}x${img.height}) exceed maximum size of 1000x1000 pixels`,
              });
            } else {
              resolve({ isValid: true, message: "" });
            }
          };

          img.onerror = () => {
            clearTimeout(timeoutId);
            console.error("Image failed to load in validation");
            resolve({
              isValid: false,
              message: "Unable to load image. The file may be corrupted.",
            });
          };

          // Set the image source from the data URL
          img.src = dataUrl;
        };

        reader.onerror = () => {
          console.error("FileReader error:", reader.error);
          resolve({
            isValid: false,
            message: "Error reading the file. Please try another image.",
          });
        };

        // Read the file as a data URL
        reader.readAsDataURL(file);
      });
    },
    []
  );

  // Handle logo upload with preview
  const handleLogoUploadWithPreview = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      // Mark logo field as touched
      setTouchedFields((prev) => ({
        ...prev,
        logoImage: true,
      }));

      if (e.target.files && e.target.files.length > 0) {
        const file = e.target.files[0];
        console.log("File selected:", file.name, file.type, file.size);

        // Validate file size (max 1MB)
        if (file.size > 1 * 1024 * 1024) {
          setValidationErrors((prev) => ({
            ...prev,
            logoImage: "Image file is too large. Maximum size is 1MB",
          }));
          return;
        }

        try {
          // Set loading state immediately
          setImagePreviewStatus("loading");

          // Create a data URL for preview
          const reader = new FileReader();

          // Create a promise to wait for the FileReader
          const readFilePromise = new Promise<string>((resolve, reject) => {
            reader.onload = (e) => {
              if (e.target?.result) {
                resolve(e.target.result as string);
              } else {
                reject(new Error("Failed to read file"));
              }
            };
            reader.onerror = () => reject(reader.error);
            reader.readAsDataURL(file);
          });

          // Wait for the file to be read
          const dataUrl = await readFilePromise;
          setLogoPreviewUrl(dataUrl);

          // Then validate dimensions
          const { isValid, message } = await validateImageDimensions(file);

          if (!isValid) {
            // If validation fails, show error but keep the preview
            setValidationErrors((prev) => ({
              ...prev,
              logoImage: message,
            }));
            return;
          }

          // Clear any previous errors
          setValidationErrors((prev) => ({
            ...prev,
            logoImage: "",
          }));

          // Update form data
          setFormData((prev) => ({
            ...prev,
            logoImage: file,
          }));

          // Mark form as modified since logo has changed
          setIsFormModified(true);
        } catch (error) {
          console.error("Error handling image upload:", error);
          setValidationErrors((prev) => ({
            ...prev,
            logoImage: "Error processing image. Please try another file.",
          }));
        }
      }
    },
    [validateImageDimensions]
  );

  // Function to save white label settings to the API
  const saveWhiteLabelSettings = useCallback(
    async (data: WhiteLabelFormData): Promise<ApiResponse> => {
      setIsLoading(true);
      setError(null);

      try {
        // Create FormData object for file upload
        const formData = new FormData();
        formData.append("brand_name", data.brandName);
        if (data.logoImage) {
          formData.append("logo", data.logoImage);
        }
        // Only append phone number if it has a value
        if (data.phoneNumber) {
          // Combine country code and phone number
          const fullPhoneNumber = `${data.countryCode}${data.phoneNumber}`;
          formData.append("phone_number", fullPhoneNumber);
        }
        // Only append website if it has a value
        if (data.website) {
          // Ensure website has https:// prefix
          const websiteUrl = data.website;
          try {
            // Check if it's already a valid URL
            new URL(websiteUrl);
            // If it is, make sure it uses https
            formData.append("website", websiteUrl);
          } catch (e) {
            // If not a valid URL, don't send it
            console.error("Invalid website URL not sent to API:", websiteUrl);
          }
        }

        // Use the appropriate method based on whether we have existing settings
        let response;
        if (hasExistingSettings) {
          // Use PATCH to update existing settings
          response = await whiteLabelService.updateWhiteLabelSettings(formData);
        } else {
          // Use POST to create new settings
          response = await whiteLabelService.saveWhiteLabelSettings(formData);
        }

        return response;
      } catch (error: any) {
        console.error("Error saving white label settings:", error);
        return {
          success: false,
          error: "Failed to save white label settings. Please try again.",
        };
      } finally {
        setIsLoading(false);
      }
    },
    [hasExistingSettings]
  );

  // Handle next button click
  const handleNextClick = async () => {
    // Mark all fields as touched for validation
    setTouchedFields({
      brandName: true,
      phoneNumber: true,
      website: true,
      logoImage: true,
    });

    // Validate all fields
    const brandNameError = validateBrandName(inputValues.brandName);
    const phoneNumberError = validatePhoneNumber(inputValues.phoneNumber);
    const websiteError = validateWebsite(inputValues.website);
    const logoError = validationErrors.logoImage;

    // Update validation errors
    setValidationErrors({
      brandName: brandNameError,
      phoneNumber: phoneNumberError,
      website: websiteError,
      logoImage: logoError,
    });

    // Check if there are any validation errors
    // Only brand name is required, other fields are optional
    if (brandNameError || logoError) {
      setError("Please correct the errors in the form before proceeding");
      return;
    }

    // Save form data from input values
    const updatedFormData = {
      ...formData,
      brandName: inputValues.brandName,
      phoneNumber: inputValues.phoneNumber,
      website: inputValues.website,
      countryCode: selectedCountry.dialCode,
    };

    setFormData(updatedFormData);

    // If form hasn't been modified and we have existing settings, skip API call
    if (!isFormModified && hasExistingSettings) {
      console.log("Form not modified, skipping API call");
      // Move to next step without calling API
      onNext(updatedFormData);
      return;
    }

    // Call API to save white label settings only if form has been modified
    setIsLoading(true);

    try {
      const result = await saveWhiteLabelSettings(updatedFormData);

      if (!result.success) {
        // If API call failed, show error and don't proceed
        setError(result.error || "Failed to save white label settings");
        return;
      }

      // If API call succeeded, move to next step
      onNext(updatedFormData);
    } catch (error) {
      console.error("Exception during API call:", error);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-3 sm:p-4 overflow-y-auto">
      <h2 className="text-lg sm:text-xl font-bold text-secondary mb-2 sm:mb-4">
        {hasExistingSettings
          ? "Update White Label Settings"
          : "White Label Information"}
      </h2>
      <p className="text-sm sm:text-base text-secondary mb-4 sm:mb-6">
        {hasExistingSettings
          ? "Update your brand details for your white label reports."
          : "Enter your brand details to customize your white label report."}
      </p>

      <div className="space-y-4 sm:space-y-6">
        <div>
          <label className="block text-sm sm:text-base text-secondary mb-1">
            <span className="text-red-500">*</span> Brand name:
          </label>
          <input
            type="text"
            value={inputValues.brandName}
            onChange={(e) => handleInputChange("brandName", e.target.value)}
            placeholder="Type Your Brand Name Here..."
            className={`w-full p-2 sm:p-3 text-sm sm:text-base border ${
              touchedFields.brandName && validationErrors.brandName
                ? "border-red-500 bg-red-50"
                : "border-gray-300"
            } rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50`}
            required
          />
          {touchedFields.brandName && validationErrors.brandName && (
            <p className="text-red-500 text-xs sm:text-sm mt-1">
              {validationErrors.brandName}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm sm:text-base text-secondary mb-1">
            <span className="text-red-500">*</span> Logo:
          </label>
          <div
            className={`border border-dashed ${
              touchedFields.logoImage && validationErrors.logoImage
                ? "border-red-500 bg-red-50"
                : "border-gray-300"
            } rounded-lg p-3 sm:p-4 text-center`}
          >
            {logoPreviewUrl ? (
              <div className="flex flex-col items-center">
                <div className="w-full max-w-xs h-32 sm:h-40 mb-3 sm:mb-4 flex items-center justify-center bg-gray-50 rounded-lg overflow-hidden relative">
                  {imagePreviewStatus === "loading" && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-70 z-10">
                      <div className="w-5 h-5 sm:w-6 sm:h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                  {imagePreviewStatus === "error" && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
                      <div className="text-red-500 text-xs sm:text-sm px-4 text-center">
                        Failed to load image preview
                      </div>
                    </div>
                  )}
                  {logoPreviewUrl && (
                    <img
                      src={logoPreviewUrl}
                      alt="Brand Logo Preview"
                      className="max-h-32 sm:max-h-40 max-w-full object-contain p-2"
                      onLoad={() => {
                        console.log("Image loaded successfully");
                        setImagePreviewStatus("loaded");
                      }}
                      onError={(e) => {
                        console.error(
                          "Image failed to load in preview:",
                          logoPreviewUrl
                        );
                        setImagePreviewStatus("error");
                        // Add fallback behavior or error message
                        const imgElement = e.currentTarget as HTMLImageElement;
                        imgElement.style.display = "none"; // Hide the broken image
                      }}
                    />
                  )}
                </div>
                <div className="flex space-x-4">
                  <button
                    onClick={() => {
                      // Simply clear the preview URL and reset form data
                      setLogoPreviewUrl(null);
                      setImagePreviewStatus("loading");
                      setFormData((prev) => ({ ...prev, logoImage: null }));
                      setValidationErrors((prev) => ({
                        ...prev,
                        logoImage: "",
                      }));

                      // Mark form as modified since logo has been removed
                      if (initialFormData.logoImage || externalLogoUrl) {
                        setIsFormModified(true);
                      }
                    }}
                    className="text-red-500 text-sm sm:text-base underline"
                  >
                    Remove
                  </button>
                </div>
              </div>
            ) : (
              <>
                <div className="text-gray-400 mb-2 text-sm sm:text-base">
                  + Upload Your Brand Logo
                </div>
                <div className="text-xs text-gray-500 mb-2">
                  PNG format only, max 1MB size, max 1000x1000 pixels
                </div>
                <input
                  type="file"
                  accept="image/png"
                  onChange={handleLogoUploadWithPreview}
                  className="hidden"
                  id="logo-upload"
                />
                <label
                  htmlFor="logo-upload"
                  className="cursor-pointer inline-block px-3 py-1.5 sm:px-4 sm:py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  Select File
                </label>
              </>
            )}
          </div>
          {touchedFields.logoImage && validationErrors.logoImage && (
            <p className="text-red-500 text-xs sm:text-sm mt-1">
              {validationErrors.logoImage}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm sm:text-base text-secondary mb-1">
            Phone number:
          </label>
          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
            <CountrySelector
              selectedCountry={selectedCountry}
              onSelect={setSelectedCountry}
              className="w-full sm:w-32 flex-shrink-0 mb-2 sm:mb-0"
            />
            <input
              type="text"
              value={inputValues.phoneNumber}
              onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
              placeholder="Type Your Phone Number Here..."
              className={`flex-1 p-2 sm:p-3 text-sm sm:text-base border ${
                touchedFields.phoneNumber && validationErrors.phoneNumber
                  ? "border-red-500 bg-red-50"
                  : "border-gray-300"
              } rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50`}
            />
          </div>
          {touchedFields.phoneNumber && validationErrors.phoneNumber && (
            <p className="text-red-500 text-xs sm:text-sm mt-1">
              {validationErrors.phoneNumber}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm sm:text-base text-secondary mb-1">
            Website:
          </label>
          <input
            type="url"
            value={inputValues.website}
            onChange={(e) => handleInputChange("website", e.target.value)}
            placeholder="https://example.com"
            className={`w-full p-2 sm:p-3 text-sm sm:text-base border ${
              touchedFields.website && validationErrors.website
                ? "border-red-500 bg-red-50"
                : "border-gray-300"
            } rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50`}
          />
          {touchedFields.website && validationErrors.website && (
            <p className="text-red-500 text-xs sm:text-sm mt-1">
              {validationErrors.website}
            </p>
          )}
        </div>
      </div>

      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-lg text-sm sm:text-base">
          {error}
        </div>
      )}

      <div className="mt-4 sm:mt-6 flex justify-end">
        <button
          type="button"
          onClick={handleNextClick}
          className="btn btn--primary px-4 sm:px-6 py-2 text-sm sm:text-base"
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="flex items-center">
              <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              <span className="text-sm sm:text-base">Saving settings...</span>
            </div>
          ) : hasExistingSettings && isFormModified ? (
            "Update Settings"
          ) : (
            "Next Step"
          )}
        </button>
      </div>
    </div>
  );
}
