import RHTextBox from "@/ui/RHTextBox";
import { FormEvent, useState } from "react";

type Props = {
  setStep: (step: number) => void;
};

export default function ResultPDFForm({ setStep }: Props) {
  const [fullName, setFullName] = useState("");

  const onSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setStep(fullName ? 2 : 1);
  };

  return (
    <form
      onSubmit={onSubmit}
      autoComplete="off"
      method="post"
      action="javascript:void(0);"
      data-form-type="pdf-download"
    >
      <div className="flex flex-col gap-4 mb-8">
        <RHTextBox
          label="Full name"
          placeholder="Type Your Full name here."
          type="text"
          name="fullName"
          value={fullName}
          onChange={(e) => setFullName(e)}
        />
        <RHTextBox
          label="Email Address"
          placeholder="Type Your Email Address here."
          type="email"
          name="email"
          autoComplete="username email"
          onChange={(e) => console.log(e)}
        />
        <RHTextBox
          label="Password"
          placeholder="Type Your Password here."
          type="password"
          name="password"
          autoComplete="new-password"
          onChange={(e) => console.log(e)}
        />
      </div>
      <button className="btn btn--primary !w-full !text-xl !py-4">
        Confirm
      </button>
    </form>
  );
}
