"use client";
import { useRef, useState, useEffect } from "react";
import Radar<PERSON>hart from "@/ui/charts/RadarChart";
import { CopyIcon, DownloadIcon } from "@/ui/icons/general";
import { motion, AnimatePresence } from "framer-motion";
import Modal from "@/ui/Modal";
import Link from "next/link";
import { SeoAnalyzerPdf } from "@/app/(root)/audit/_/components/pdf";
import { useAuditStore } from "@/store/auditStore";
import WhiteLabelModal from "./WhiteLabelModal";
import { PaymentStatusResponse } from "@/services/paymentService";
import { useRequireLogin } from "@/utils/requireLogin";

import { useReactToPrint } from "react-to-print";

// Define types for the SEO ancalysis data structure
interface TotalScore {
  score: number;
  grade: string;
}

interface TitleTag {
  recommendation?: string;
  score?: number;
  [key: string]: unknown;
}

interface AnalysisSection {
  total_score: TotalScore;
  title_tag?: TitleTag;
  [key: string]: unknown;
}

interface SeoAnalysisData {
  onpage_analysis?: AnalysisSection;
  usability_analysis?: AnalysisSection;
  technology_review_analysis?: AnalysisSection;
  localseo_analysis?: AnalysisSection;
  links_analysis?: AnalysisSection;
  social_analysis?: AnalysisSection;
  results?: {
    onpage_analysis?: AnalysisSection;
    usability_analysis?: AnalysisSection;
    technology_review_analysis?: AnalysisSection;
    localseo_analysis?: AnalysisSection;
    links_analysis?: AnalysisSection;
    social_analysis?: AnalysisSection;
  };
  [key: string]: unknown;
}

// Helper function to safely extract scores from analysis data
const extractScore = (
  data: Record<string, unknown>,
  path: string[],
  defaultValue = 0
): number => {
  try {
    let current: unknown = data;
    for (const key of path) {
      if (!current || typeof current !== "object") return defaultValue;
      current = (current as Record<string, unknown>)[key];
    }
    return typeof current === "number" ? current : defaultValue;
  } catch {
    return defaultValue;
  }
};

export default function SideAudit({
  result,
  success,
  taskId,
  paymentStatus,
  showWhiteLabelModal: initialShowWhiteLabelModal,
  isLoadingPayment,
  onWhiteLabelModalClose,
  urlPaymentId,
}: {
  result: SeoAnalysisData;
  success: boolean;
  taskId?: string;
  paymentStatus?: PaymentStatusResponse | null;
  showWhiteLabelModal?: boolean;
  isLoadingPayment?: boolean;
  onWhiteLabelModalClose?: () => void;
  urlPaymentId?: string;
}) {
  const urlName = useAuditStore((s) => s.websiteUrl);
  const contentRef = useRef<HTMLDivElement>(null);
  const [isPdfReady, setIsPdfReady] = useState(false);
  const [pdfError, setPdfError] = useState<string | null>(null);

  // Effect to set PDF ready state when component mounts and result data is available
  useEffect(() => {
    if (result && Object.keys(result).length > 0) {
      // Set a small delay to ensure all data is processed
      const timer = setTimeout(() => {
        setIsPdfReady(true);
      }, 1000);

      // Listen for the custom event from the PDF component
      const handlePdfReady = (event: Event) => {
        const customEvent = event as CustomEvent;
        if (customEvent.detail?.ready) {
          setIsPdfReady(true);
          if (customEvent.detail?.timedOut) {
            setPdfError("");
          }
        }
      };

      // Add event listener
      window.addEventListener("pdf-content-ready", handlePdfReady);

      return () => {
        clearTimeout(timer);
        window.removeEventListener("pdf-content-ready", handlePdfReady);
      };
    }
  }, [result]);

  // Enhanced configuration for react-to-print with better image handling
  const reactToPrintFn = useReactToPrint({
    // Use the contentRef to reference the component to be printed
    contentRef,
    documentTitle: `SEO Audit-${urlName}`,
    onBeforePrint: () => {
      setIsPrintLoading(true);
      return new Promise<void>((resolve) => {
        if (isPdfReady) {
          // If PDF is already ready, proceed immediately
          resolve();
        } else {
          console.warn("PDF content may not be fully loaded, waiting...");

          // Set a timeout to prevent infinite waiting
          const timeout = setTimeout(() => {
            console.warn("PDF content timeout, proceeding anyway");
            setIsPdfReady(true);
            resolve();
          }, 3000);

          // Listen for the ready event
          const handlePdfReady = () => {
            clearTimeout(timeout);
            resolve();
          };

          window.addEventListener("pdf-content-ready", handlePdfReady, {
            once: true,
          });
        }
      });
    },
    onAfterPrint: () => {
      setIsPrintLoading(false);
    },
    onPrintError: (error) => {
      console.error("Print failed:", error);
      setPdfError("Failed to print. Please try again.");
      setIsPrintLoading(false);
    },
    pageStyle: `
      @page {
        size: A4 portrait;
        margin: 0.5cm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
          color-adjust: exact !important;
        }
        img {
          display: block !important;
          break-inside: avoid !important;
          max-width: 100% !important;
          visibility: visible !important;
        }
        span[style*="box-sizing: border-box"] {
          display: block !important;
          visibility: visible !important;
        }
        /* Ensure watermark text is never transformed */
        .pdf-container * {
          text-transform: none !important;
          font-variant: normal !important;
        }
        .watermark-container *,
        .section-watermark *,
        .print-watermark *,
        [data-watermark],
        [data-watermark] * {
          text-transform: none !important;
          font-variant: normal !important;
        }
      }
    `,
  });
  const [copied, setCopied] = useState(false);
  const [showPdfModal, setShowPdfModal] = useState(false);
  const [showWhiteLabelModal, setShowWhiteLabelModal] = useState(
    initialShowWhiteLabelModal || false
  );
  const [shareButtonClicked, setShareButtonClicked] = useState(false);
  const [downloadButtonClicked, setDownloadButtonClicked] = useState(false);
  const [whiteLabelButtonClicked, setWhiteLabelButtonClicked] = useState(false);
  const [isDownloadLoading, setIsDownloadLoading] = useState(false);
  const [isPrintLoading, setIsPrintLoading] = useState(false);

  // Use the requireLogin hook for authentication checks
  const requireLoginForPdf = useRequireLogin("pdf");
  const requireLoginForShare = useRequireLogin("share");
  const requireLoginForWhiteLabel = useRequireLogin("whiteLabel");

  // Use the success prop passed from the parent component
  const isAnalysisComplete = success;

  // Effect to sync the WhiteLabelModal state with the prop
  useEffect(() => {
    if (initialShowWhiteLabelModal !== undefined) {
      setShowWhiteLabelModal(initialShowWhiteLabelModal);
    }
  }, [initialShowWhiteLabelModal]);

  // Mapping of radar chart labels to section IDs for navigation
  const labelToSectionMap: Record<string, string> = {
    Performance: "performance",
    "On-Page": "onPageSEO",
    Technology: "technology",
    Social: "social",
    Usability: "usability",
  };

  // Navigation function for radar chart clicks
  const handleRadarChartClick = (label: string) => {
    const sectionId = labelToSectionMap[label];
    if (sectionId) {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    }
  };

  // Process the analysis data for the radar chart
  const processAnalysisData = () => {
    if (!result) return { labels: [], data: [] };

    // Define the categories and their paths in the data structure
    const categories = [
      {
        label: "Performance",
        path: ["performance_analysis", "total_score", "score"],
      },
      { label: "On-Page", path: ["onpage_analysis", "total_score", "score"] },
      {
        label: "Technology",
        path: ["technology_review_analysis", "total_score", "score"],
      },
      // {
      //   label: "Local SEO",
      //   path: ["localseo_analysis", "total_score", "score"],
      // },
      {
        label: "Social",
        path: ["social_analysis", "total_score", "score"],
      },
      {
        label: "Usability",
        path: ["usability_analysis", "total_score", "score"],
      },
    ];

    // Extract scores for each category
    const data = categories.map((category) => {
      // First try to get the score directly from result
      let score = extractScore(result, category.path);

      // If score is 0 and result.results exists, try to get it from result.results
      // This allows actual 0 scores to be displayed when data is loaded but score is 0
      if (score === 0 && result.results) {
        const nestedPath = category.path.slice(); // Clone the path array
        score = extractScore(result.results, nestedPath);
      }

      // Return the score as is, allowing 0 values for when no data is loaded
      return score;
    });

    // Extract labels
    const labels = categories.map((category) => category.label);

    return { labels, data };
  };

  const copyToClipboard = () => {
    // Only proceed if analysis is complete
    if (isAnalysisComplete) {
      // Use requireLoginForShare to handle authentication check and callback
      requireLoginForShare(() => {
        // This code will only run if the user is authenticated
        if (typeof window !== "undefined") {
          // Always copy a clean /audit?share=ID URL without hash fragments
          const url = new URL("/audit", window.location.origin);
          if (taskId) {
            url.searchParams.set("share", taskId);
          }
          navigator.clipboard.writeText(url.toString());
          setCopied(true);
          setTimeout(() => {
            setCopied(false);
          }, 1000);
        }
      });
    } else {
      // Show "Still Analyzing..." message temporarily
      setShareButtonClicked(true);
      setTimeout(() => {
        setShareButtonClicked(false);
      }, 2000);
    }
  };

  const showPdfHandler = async () => {
    // Only proceed if analysis is complete
    if (isAnalysisComplete) {
      // Use requireLoginForPdf to handle authentication check and callback
      requireLoginForPdf(async () => {
        // This code will only run if the user is authenticated
        setIsDownloadLoading(true);

        // Add a small delay to show the loading animation
        await new Promise((resolve) => setTimeout(resolve, 150));

        setShowPdfModal(true);
        setIsDownloadLoading(false);
      });
    } else {
      // Show "Still Analyzing..." message temporarily
      setDownloadButtonClicked(true);
      setTimeout(() => {
        setDownloadButtonClicked(false);
      }, 2000);
    }
  };

  const showWhiteLabelHandler = () => {
    // Only proceed if analysis is complete
    if (isAnalysisComplete) {
      // Use requireLoginForWhiteLabel to handle authentication check and callback
      requireLoginForWhiteLabel(() => {
        // This code will only run if the user is authenticated
        setShowWhiteLabelModal(true);
      });
    } else {
      // Show "Still Analyzing..." message temporarily
      setWhiteLabelButtonClicked(true);
      setTimeout(() => {
        setWhiteLabelButtonClicked(false);
      }, 2000);
    }
  };

  return (
    <>
      {/* Hidden PDF component for preloading - This ensures the component is mounted at page load */}
      <div className="hidden">
        <div
          ref={contentRef}
          className="pdf-container pt-0 w-full mx-auto transform origin-top-center md:scale-[1]"
          style={{ minHeight: "100%" }}
        >
          <SeoAnalyzerPdf
            urlName={urlName}
            screenshotUrl={
              typeof result.desktop_screenshot_url === "string"
                ? result.desktop_screenshot_url
                : null
            }
            onPageSeoData={(result.onpage_analysis as any) || {}}
            usabilityData={(result.usability_analysis as any) || {}}
            technologyData={(result.technology_review_analysis as any) || {}}
            socialData={(result.social_analysis as any) || {}}
            performanceData={(result.performance_analysis as any) || {}}
            linksData={(result.links_analysis as any) || {}}
            pagespeedData={(result.pagespeed_analysis as any) || {}}
            pagespeedMobileData={
              (result.pagespeed_mobile_analysis as any) || {}
            }
            localSeoData={(result.localseo_analysis as any) || {}}
            childPagesData={(result.child_pages as any) || []}
            brand_name="SEO ANALYSER"
            brand_website="https://www.seoanalyser.com.au"
            brand_photo="/images/appLogo.svg"
          />
        </div>
      </div>

      <div className="w-full bg-white p-3 py-5 rounded-2xl">
        {/* White Label Modal */}
        {showWhiteLabelModal && (
          <WhiteLabelModal
            key={`white-label-modal-${Date.now()}`} // Add a key that changes each time to force remount
            isOpen={showWhiteLabelModal}
            onClose={() => {
              setShowWhiteLabelModal(false);
              if (onWhiteLabelModalClose) {
                onWhiteLabelModalClose();
              }
            }}
            onSuccess={() => {
              // Handle success (e.g., show a success message)
              
            }}
            taskId={taskId}
            paymentStatus={paymentStatus}
            isLoadingPayment={isLoadingPayment}
            initialStep={paymentStatus ? "download" : undefined}
            urlPaymentId={urlPaymentId}
          />
        )}

        {/* PDF Modal */}
        <Modal
          open={showPdfModal}
          onClose={() => setShowPdfModal(false)}
          title="SEO Analysis PDF Report"
          size="2xl"
        >
          <div className="flex flex-col h-full">
            {/* Scrollable content area */}
            <div className="flex-1 overflow-y-auto overflow-x-hidden pb-16 px-2 border-b-2 border-gray-200">
              {!isPdfReady ? (
                <div className="flex flex-col items-center justify-center py-20">
                  <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                  <p className="mt-4 text-gray-600">Preparing PDF content...</p>
                </div>
              ) : (
                <div className="pdf-container pt-0 w-full max-w-full mx-auto transform origin-top-center overflow-x-hidden">
                  {/* We don't need ref here since we're using the hidden component for printing */}
                  <div className="w-full max-w-full overflow-hidden">
                    <SeoAnalyzerPdf
                      urlName={urlName}
                      screenshotUrl={
                        typeof result.desktop_screenshot_url === "string"
                          ? result.desktop_screenshot_url
                          : null
                      }
                      onPageSeoData={(result.onpage_analysis as any) || {}}
                      usabilityData={(result.usability_analysis as any) || {}}
                      technologyData={
                        (result.technology_review_analysis as any) || {}
                      }
                      socialData={(result.social_analysis as any) || {}}
                      performanceData={
                        (result.performance_analysis as any) || {}
                      }
                      linksData={(result.links_analysis as any) || {}}
                      pagespeedData={(result.pagespeed_analysis as any) || {}}
                      pagespeedMobileData={
                        (result.pagespeed_mobile_analysis as any) || {}
                      }
                      localSeoData={(result.localseo_analysis as any) || {}}
                      childPagesData={(result.child_pages as any) || []}
                      brand_name="SEO ANALYSER"
                      brand_website="https://www.seoanalyser.com.au"
                      brand_photo="/images/appLogo.svg"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Sticky print button */}
            <div className="py-4 px-6 bg-white sticky bottom-0 left-0 right-0 flex justify-between">
              {pdfError && (
                <div className="text-red-500 flex items-center">
                  <span>{pdfError}</span>
                </div>
              )}
              <div className="ml-auto">
                <button
                  onClick={reactToPrintFn}
                  disabled={!isPdfReady || isPrintLoading}
                  className={`px-6 py-3.5 ${
                    isPdfReady && !isPrintLoading
                      ? "bg-primary hover:bg-primary/90"
                      : "bg-gray-400 cursor-not-allowed"
                  } text-white rounded-lg transition-all duration-200 flex items-center gap-2 font-semibold relative overflow-hidden`}
                >
                  <AnimatePresence mode="wait">
                    {isPrintLoading ? (
                      <motion.div
                        key="printing"
                        initial={{ y: -30, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        exit={{ y: 30, opacity: 0 }}
                        transition={{ duration: 0.1 }}
                        className="flex items-center gap-2"
                      >
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{
                            duration: 0.6,
                            repeat: Infinity,
                            ease: "linear",
                          }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full flex-shrink-0"
                        />
                        <span className="whitespace-nowrap">
                          Generating PDF...
                        </span>
                      </motion.div>
                    ) : (
                      <motion.div
                        key="ready"
                        initial={{ y: -30, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        exit={{ y: 30, opacity: 0 }}
                        transition={{ duration: 0.1 }}
                        className="flex items-center gap-2"
                      >
                        <DownloadIcon className="w-5 h-5 flex-shrink-0" />
                        <span className="whitespace-nowrap">
                          {isPdfReady
                            ? "Print/Save as PDF"
                            : "Preparing PDF..."}
                        </span>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </button>
              </div>
            </div>
          </div>
        </Modal>

        <div className="w-full border border-light-gray rounded-lg px-3 pt-4 pb-4">
          <div className="text-secondary text-sm font-bold mb-0.5">
            SEO Performance Breakdown
          </div>
          <div className="mt-1">
            {(() => {
              // Process the data for the radar chart
              const { labels, data } = processAnalysisData();
              return (
                <RadarChart
                  data={data}
                  labels={labels}
                  onLabelClick={handleRadarChartClick}
                />
              );
            })()}
          </div>
        </div>
        <div className="w-full p-3 rounded-lg bg-primary/9 mt-4">
          <div className="font-semibold text-primary">
            {(() => {
              // Get the data to determine the message
              const { data } = processAnalysisData();
              const avgScore =
                data.length > 0
                  ? data.reduce((sum, score) => sum + score, 0) / data.length
                  : 0;

              if (avgScore >= 80) return "Your Page is Performing Well";
              if (avgScore >= 50) return "Your Page Needs Some Improvements";
              return "Your Page Needs Significant Improvements";
            })()}
          </div>
          <div className="text-sm text-primary/80 mt-1">
            {(() => {
              // Get the data and labels for detailed analysis
              const { data, labels } = processAnalysisData();

              // Skip if no data
              if (!data.length)
                return "Analyze your SEO scores across different categories to identify areas for improvement.";

              // Group categories by performance level
              const needsSignificantImprovement = [];
              const needsSomeImprovement = [];
              const performingWell = [];

              // Categorize each section based on its score
              for (let i = 0; i < data.length; i++) {
                const score = data[i];
                const label = labels[i];

                if (score < 50) {
                  needsSignificantImprovement.push(label);
                } else if (score < 80) {
                  needsSomeImprovement.push(label);
                } else {
                  performingWell.push(label);
                }
              }

              // Build the summary message as a single text
              let summaryMessage = "";

              // Only include the needs improvement sections
              if (needsSignificantImprovement.length) {
                summaryMessage += `Your page needs significant improvement in these sections: ${needsSignificantImprovement.join(
                  ", "
                )}.`;
              }

              if (needsSomeImprovement.length) {
                if (summaryMessage) summaryMessage += " ";
                summaryMessage += `You need some improvement in these other sections: ${needsSomeImprovement.join(
                  ", "
                )}.`;
              }

              return (
                summaryMessage ||
                "Analyze your SEO scores across different categories to identify areas for improvement."
              );
            })()}
          </div>
        </div>
        <div className="flex flex-col gap-1 mt-2">
          <button
            onClick={copyToClipboard}
            disabled={!isAnalysisComplete}
            className={`w-full btn ${
              isAnalysisComplete
                ? "btn--outline"
                : "btn--outline  opacity-70 cursor-not-allowed"
            } mt-2 relative overflow-hidden h-11`}
          >
            <AnimatePresence mode="wait">
              {copied ? (
                <motion.div
                  key="copied"
                  initial={{ y: -50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 50, opacity: 0 }}
                  transition={{ duration: 0.15 }}
                  className="absolute inset-0 flex items-center justify-center"
                >
                  Copied!
                </motion.div>
              ) : shareButtonClicked && !isAnalysisComplete ? (
                <motion.div
                  key="analyzing-share"
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 50, opacity: 0 }}
                  transition={{ duration: 0.15 }}
                  className="absolute inset-0 flex items-center justify-center"
                >
                  <span className="flex items-center">
                    Still Analyzing
                    <motion.span
                      animate={{ opacity: [0, 1, 0] }}
                      transition={{ repeat: Infinity, duration: 1.5 }}
                    >
                      ...
                    </motion.span>
                  </span>
                </motion.div>
              ) : (
                <motion.div
                  key="share"
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 50, opacity: 0 }}
                  transition={{ duration: 0.15 }}
                  className="absolute inset-0 flex items-center justify-center"
                >
                  <CopyIcon className="w-5 h-5 mr-2" /> Share
                </motion.div>
              )}
            </AnimatePresence>
          </button>

          <button
            onClick={showPdfHandler}
            disabled={!isAnalysisComplete || isDownloadLoading}
            className={`w-full text-primary font-bold border border-primary rounded-lg  ${
              isAnalysisComplete && !isDownloadLoading
                ? "opacity-100 hover:bg-primary hover:text-white transition-all duration-200"
                : " border-primary opacity-70 cursor-not-allowed"
            } mt-2 h-11 flex items-center justify-center relative overflow-hidden`}
            style={{ minWidth: "140px" }}
          >
            <AnimatePresence mode="wait">
              {isDownloadLoading ? (
                <motion.div
                  key="loading-download"
                  initial={{ y: -50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 50, opacity: 0 }}
                  transition={{ duration: 0.1 }}
                  className="flex items-center justify-center w-full"
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 0.6,
                      repeat: Infinity,
                      ease: "linear",
                    }}
                    className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full mr-2 flex-shrink-0"
                  />
                  <span className="whitespace-nowrap">Opening PDF...</span>
                </motion.div>
              ) : downloadButtonClicked && !isAnalysisComplete ? (
                <motion.div
                  key="analyzing-download"
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 50, opacity: 0 }}
                  transition={{ duration: 0.1 }}
                  className="flex items-center justify-center w-full"
                >
                  <span className="flex items-center whitespace-nowrap">
                    Still Analyzing
                    <motion.span
                      animate={{ opacity: [0, 1, 0] }}
                      transition={{ repeat: Infinity, duration: 1.2 }}
                    >
                      ...
                    </motion.span>
                  </span>
                </motion.div>
              ) : (
                <motion.div
                  key="download"
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 50, opacity: 0 }}
                  transition={{ duration: 0.1 }}
                  className="flex items-center justify-center w-full"
                >
                  <DownloadIcon className="w-5 h-5 mr-2 text-sm flex-shrink-0" />
                  <span className="whitespace-nowrap">Download PDF</span>
                </motion.div>
              )}
            </AnimatePresence>
          </button>
          <button
            onClick={showWhiteLabelHandler}
            disabled={!isAnalysisComplete}
            className={`w-full btn ${
              isAnalysisComplete
                ? "btn--primary"
                : "btn--primary  opacity-70 cursor-not-allowed"
            } mt-2 relative overflow-hidden h-11`}
          >
            <AnimatePresence mode="wait">
              {whiteLabelButtonClicked && !isAnalysisComplete ? (
                <motion.div
                  key="analyzing-white-label"
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 50, opacity: 0 }}
                  transition={{ duration: 0.15 }}
                  className="absolute inset-0 flex items-center justify-center"
                >
                  <span className="flex items-center">
                    Still Analyzing
                    <motion.span
                      animate={{ opacity: [0, 1, 0] }}
                      transition={{ repeat: Infinity, duration: 1.5 }}
                    >
                      ...
                    </motion.span>
                  </span>
                </motion.div>
              ) : (
                <motion.div
                  key="white-label"
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 50, opacity: 0 }}
                  transition={{ duration: 0.15 }}
                  className="absolute inset-0 flex items-center justify-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.79416 11.8754L10.0666 7.60292M8.1246 14.2058L10.0666 12.2638M9.99983 1.45521L1.45488 10.0002C1.16362 10.2915 1 10.6866 1 11.0986C1 11.5105 1.16362 11.9056 1.45488 12.197L7.803 18.5451C8.09435 18.8364 8.48945 19 8.90142 19C9.31338 19 9.70849 18.8364 9.99983 18.5451L18.5448 10.0002C18.8362 9.70887 18.9999 9.31376 19 8.90175V2.55363C19 2.14158 18.8363 1.74641 18.545 1.45505C18.2536 1.16369 17.8584 1 17.4464 1H11.0982C10.6862 1.00009 10.2911 1.16383 9.99983 1.45521ZM16.4845 4.42239C16.4845 4.66109 16.3897 4.89001 16.2209 5.05879C16.0521 5.22757 15.8232 5.32239 15.5845 5.32239C15.3458 5.32239 15.1169 5.22757 14.9481 5.05879C14.7793 4.89001 14.6845 4.66109 14.6845 4.42239C14.6845 4.1837 14.7793 3.95478 14.9481 3.786C15.1169 3.61721 15.3458 3.52239 15.5845 3.52239C15.8232 3.52239 16.0521 3.61721 16.2209 3.786C16.3897 3.95478 16.4845 4.1837 16.4845 4.42239Z"
                      stroke="currentColor"
                      strokeWidth="1.2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>{" "}
                  White Label
                </motion.div>
              )}
            </AnimatePresence>
          </button>
        </div>
      </div>

      {/* <div className="w-full bg-primary p-4 lg:p-5 lg:!min-h-[260px] flex justify-between flex-col rounded-2xl mt-2 text-white">
        <div className="text-xl lg:text-2xl font-black">
          Premium SEO Toolbox
        </div>
        <p className="mt-3 lg:mt-2 text-sm lg:text-base">
          Supercharge your site’s visibility with powerful SEO tools to boost
          rankings, drive traffic, and grow fast.
        </p>
        <Link
          href="/pricing"
          className="!w-full lg:!w-auto btn btn--primary !bg-white !text-primary !border-white mt-4 py-2.5"
        >
          Check Them Out
        </Link>
      </div> */}
    </>
  );
}
