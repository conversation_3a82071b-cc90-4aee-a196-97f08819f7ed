"use client";
import { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { useAuthStore } from "@/store/authStore";

type OtpVerificationFormProps = {
  email: string;
  onSuccess: () => void;
  onResendCode: () => void;
};

export default function OtpVerificationForm({
  email,
  onSuccess,
  onResendCode,
}: OtpVerificationFormProps) {
  const [otp, setOtp] = useState<string[]>(Array(6).fill(""));
  const [localError, setLocalError] = useState("");
  const [countdown, setCountdown] = useState(120);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Get functions and states from auth store using specific selectors
  const verifyEmail = useAuthStore((state) => state.verifyEmail);
  const resendOtp = useAuthStore((state) => state.resendOtp);
  const isLoading = useAuthStore((state) => state.isLoading);
  const storeError = useAuthStore((state) => state.error);

  // Use either local error or store error
  const error = localError || storeError;

  // Handle countdown for resend code
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // Optimize the change handler to reduce renders
  const handleChange = (index: number, value: string) => {
    // Only allow numbers
    if (!/^\d*$/.test(value)) return;

    // Only update if the value is different
    if (otp[index] === value) return;

    // Use functional update to avoid stale state
    setOtp((prevOtp) => {
      const newOtp = [...prevOtp];
      newOtp[index] = value.slice(0, 1); // Only take the first character
      return newOtp;
    });

    // Auto-focus next input if current input is filled
    if (value && index < 5) {
      // Focus next input immediately without setTimeout for better performance
      requestAnimationFrame(() => {
        inputRefs.current[index + 1]?.focus();
      });
    }
  };

  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    // Move to previous input on backspace if current input is empty
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text/plain").trim();

    // Check if pasted content is a 6-digit number
    if (/^\d{6}$/.test(pastedData)) {
      const newOtp = pastedData.split("");
      setOtp(newOtp);

      // Focus the last input
      inputRefs.current[5]?.focus();
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const otpValue = otp.join("");

    // Validate OTP
    if (otpValue.length !== 6) {
      setLocalError("Please enter a valid 6-digit code");
      return;
    }

    // Prevent multiple submissions
    if (isLoading) return;

    // Reset local error
    setLocalError("");

    // Call the verify email function from auth store
    const success = await verifyEmail({
      email,
      otp: otpValue,
    });

    // If verification was successful, call the success callback with a small delay
    // to ensure profile data is fully loaded
    if (success) {
      console.log(
        "OTP verification successful, calling success callback with delay"
      );

      // Small delay to ensure profile data is loaded
      setTimeout(() => {
        console.log("Executing OTP verification success callback");
        // Move to success screen
        onSuccess();
      }, 300);
    }
  };

  const [isResending, setIsResending] = useState(false);

  const handleResendCode = async () => {
    if (countdown === 0 && !isResending) {
      setIsResending(true);
      setLocalError("");

      try {
        // Call the resendOtp method from the auth store
        const success = await resendOtp(email);

        if (success) {
          // Reset the countdown
          setCountdown(120);
          // Call the onResendCode callback for any additional handling
          onResendCode();
        } else {
          // Error is already set in the store, but we can set a local error too
          setLocalError(
            "Failed to resend verification code. Please try again."
          );
        }
      } catch (err) {
        setLocalError("Failed to resend verification code. Please try again.");
      } finally {
        setIsResending(false);
      }
    }
  };

  return (
    <div className="p-4 lg:p-6 flex-1">
      <div className="text-center mb-6 md:mb-8">
        <div className="w-14 h-14 md:w-16 md:h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4 md:mb-6 mx-auto">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-7 h-7 md:w-8 md:h-8 text-primary"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </div>
        <h2 className="text-lg md:text-xl font-bold text-secondary mb-2 md:mb-3">
          Verify Your Email
        </h2>
        <p className="text-sm md:text-base text-secondary">
          We&apos;ve sent a verification code to{" "}
          <span className="font-semibold">{email}</span>
        </p>
        <p className="text-xs md:text-sm text-secondary/70 mt-1 md:mt-2">
          Enter the 6-digit code below to verify your email
        </p>
      </div>

      <form
        onSubmit={handleSubmit}
        className="h-full flex flex-col"
        autoComplete="off"
        method="post"
        action="javascript:void(0);"
        data-form-type="otp-verification"
      >
        <div className="flex-1">
          {/* CSRF token input removed */}

          <div className="flex justify-center gap-2 mb-4">
            {otp.map((digit, index) => (
              <input
                key={index}
                ref={(el) => (inputRefs.current[index] = el)}
                type="text"
                value={digit}
                onChange={(e) => handleChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={index === 0 ? handlePaste : undefined}
                className="w-10 h-12 md:w-12 md:h-14 text-center text-lg md:text-xl font-bold border border-light-gray rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
                maxLength={1}
                autoComplete="one-time-code"
                inputMode="numeric"
                autoFocus={index === 0}
                // Add performance attributes
                data-lpignore="true" // Disable LastPass autofill
                data-form-type="other" // Prevent browser autofill
                style={{ WebkitAppearance: "none" }} // Improve rendering performance
              />
            ))}
          </div>

          {error && (
            <div className="text-primary-red text-xs md:text-sm text-center mb-4">
              {error}
            </div>
          )}
        </div>

        <div className="mt-auto">
          <button
            type="submit"
            className={`btn btn--primary !w-full !py-3 md:!py-4 !text-base  ${
              isLoading ? "opacity-70" : ""
            }`}
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                Verifying
                <motion.span
                  animate={{ opacity: [0, 1, 0] }}
                  transition={{ repeat: Infinity, duration: 1.5 }}
                >
                  ...
                </motion.span>
              </span>
            ) : (
              "Verify"
            )}
          </button>

          <div className="mt-3 md:mt-4 text-center">
            <p className="text-secondary text-xs md:text-sm">
              Didn&apos;t receive the code?{" "}
              <button
                type="button"
                onClick={handleResendCode}
                className={`${
                  countdown === 0 && !isResending
                    ? "text-primary font-semibold"
                    : "text-secondary/50 cursor-not-allowed"
                }`}
                disabled={countdown > 0 || isResending}
              >
                {isResending ? (
                  <span className="flex items-center">
                    Resending
                    <motion.span
                      animate={{ opacity: [0, 1, 0] }}
                      transition={{ repeat: Infinity, duration: 1.5 }}
                    >
                      ...
                    </motion.span>
                  </span>
                ) : countdown > 0 ? (
                  `Resend in ${countdown}s`
                ) : (
                  "Resend Code"
                )}
              </button>
            </p>
          </div>
        </div>
      </form>
    </div>
  );
}
