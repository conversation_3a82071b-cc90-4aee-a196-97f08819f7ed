/**
 * Analytics Service
 *
 * Manages Google Analytics initialization and tracking based on user consent
 */

import { hasUserConsent } from "@/components/cookie-consent/CookieConsentModal";

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

const GA_ID = process.env.NEXT_PUBLIC_GA_ID || "G-TZGM6QB4G1";
const GTM_ADS_ID = "AW-17379662777";

/**
 * Initialize Google Analytics if user has given consent
 */
export const initializeAnalytics = (): void => {
  if (!GA_ID || typeof window === "undefined") return;

  if (hasUserConsent()) {
    // Initialize gtag if not already done
    if (!window.gtag) {
      window.dataLayer = window.dataLayer || [];
      window.gtag = function gtag(...args: any[]) {
        window.dataLayer.push(args);
      };
      window.gtag("js", new Date());
    }

    // Configure Google Analytics
    window.gtag("config", GA_ID, {
      page_path: window.location.pathname,
      anonymize_ip: true, // Anonymize IP addresses for privacy
      cookie_flags: "SameSite=None;Secure",
    });

    // Configure Google Ads
    window.gtag("config", GTM_ADS_ID);

    console.log("Google Analytics and Google Ads initialized with consent");
  } else {
    console.log("Google Analytics not initialized - no user consent");
  }
};

/**
 * Track a page view
 */
export const trackPageView = (url: string): void => {
  if (!hasUserConsent() || !window.gtag) return;

  // Track page view for Google Analytics
  window.gtag("config", GA_ID, {
    page_path: url,
  });

  // Track page view for Google Ads
  window.gtag("config", GTM_ADS_ID, {
    page_path: url,
  });
};

/**
 * Track a custom event
 */
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
): void => {
  if (!hasUserConsent() || !window.gtag) return;

  window.gtag("event", action, {
    event_category: category,
    event_label: label,
    value: value,
  });
};

/**
 * Disable Google Analytics tracking
 */
export const disableAnalytics = (): void => {
  if (typeof window === "undefined" || !GA_ID) return;

  // Set the Google Analytics opt-out flag
  window[`ga-disable-${GA_ID}`] = true;

  console.log("Google Analytics tracking disabled");
};

/**
 * Enable Google Analytics tracking
 */
export const enableAnalytics = (): void => {
  if (typeof window === "undefined" || !GA_ID) return;

  // Remove the Google Analytics opt-out flag
  window[`ga-disable-${GA_ID}`] = false;

  // Re-initialize analytics
  initializeAnalytics();

  console.log("Google Analytics tracking enabled");
};

const analyticsService = {
  initializeAnalytics,
  trackPageView,
  trackEvent,
  disableAnalytics,
  enableAnalytics,
};

export default analyticsService;
